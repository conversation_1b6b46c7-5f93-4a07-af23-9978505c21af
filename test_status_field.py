#!/usr/bin/env python3
"""
Test script to debug the status field issue
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ai_navigator_client import AINavigatorClient
import logging

# Set up logging to see debug messages
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def test_status_field():
    """Test creating an entity and check what status is returned"""
    
    print("🧪 Testing Status Field in Entity Creation")
    print("=" * 50)
    
    try:
        # Initialize client
        client = AINavigatorClient()
        
        # Create a simple test entity with unique name
        import time
        timestamp = int(time.time())
        test_entity = {
            "name": f"Status Test Tool {timestamp}",
            "website_url": f"https://example.com/status-test-{timestamp}",
            "short_description": "A test tool to verify status field",
            "description": "This is a test entity to check if the status field is being set correctly",
            "entity_type_id": "fd181400-c9e6-431c-a8bd-c068d0491aba",  # AI Tool type
            "status": "ACTIVE"  # Explicitly set status
        }
        
        print("🔍 Test Entity Data:")
        print(f"   Name: {test_entity['name']}")
        print(f"   URL: {test_entity['website_url']}")
        print(f"   Status: {test_entity['status']}")
        print()
        
        print("📤 Sending entity creation request...")
        result = client.create_entity(test_entity)
        
        if result:
            print("✅ Entity creation successful!")
            print(f"   Entity ID: {result.get('id', 'Unknown')}")
            print(f"   Returned Status: {result.get('status', 'NOT SET')}")
            print(f"   Name: {result.get('name', 'Unknown')}")
            
            # Check if status matches what we sent
            sent_status = "ACTIVE"
            returned_status = result.get('status', 'NOT SET')
            
            if returned_status == sent_status:
                print("🎉 SUCCESS: Status field is working correctly!")
            else:
                print(f"❌ PROBLEM: Status mismatch!")
                print(f"   Sent: {sent_status}")
                print(f"   Received: {returned_status}")
                print("   This indicates the API is ignoring or overriding the status field")
            
            print("\n📋 Full API Response:")
            import json
            print(json.dumps(result, indent=2))
            
        else:
            print("❌ Entity creation failed!")
            print("   Check the logs above for error details")
        
        return result is not None
        
    except Exception as e:
        print(f"❌ Error during status field testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_status_field()
    if success:
        print("\n✅ STATUS FIELD TEST COMPLETED")
    else:
        print("\n❌ STATUS FIELD TEST FAILED")
    
    sys.exit(0 if success else 1)
