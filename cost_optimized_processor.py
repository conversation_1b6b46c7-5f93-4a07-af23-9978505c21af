"""
Cost-Optimized Multi-Source Processor
XAI for heavy lifting + Minimal Perplexity for gap-filling
"""

import sys
sys.path.append('/app')

import json
import logging
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse, urljoin
import re
from ai_navigator_client import AINavigatorClient
from enhanced_taxonomy_service import EnhancedTaxonomyService
from entity_type_detector import EntityTypeDetector
from centralized_url_resolver import CentralizedURLResolver
from config import config
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CostOptimizedProcessor:
    def __init__(self, xai_api_key: str, perplexity_api_key: str):
        self.client = AINavigatorClient()
        self.taxonomy_service = EnhancedTaxonomyService(self.client)
        self.entity_detector = EntityTypeDetector()
        self.url_resolver = CentralizedURLResolver()
        self.xai_api_key = xai_api_key
        self.perplexity_api_key = perplexity_api_key
        
    def process_tool(self, tool_data: dict) -> bool:
        """Process a single tool with cost optimization"""
        tool_name = tool_data.get('tool_name_on_directory', '')
        futuretools_url = tool_data.get('external_website_url', '')
        
        logger.info(f"🚀 Processing: {tool_name}")
        
        try:
            # Step 1: Resolve actual website URL
            actual_url = self._resolve_actual_url(futuretools_url)
            logger.info(f"  🔍 Resolved URL: {actual_url}")
            
            # Step 2: Check if entity exists
            existing_entity = self._find_existing_entity(tool_name, actual_url)
            
            # Step 3: Scrape website comprehensively
            website_data = self._scrape_website(actual_url)
            
            # Step 4: XAI Enhancement (FREE - does heavy lifting)
            xai_enhanced = self._enhance_with_xai(tool_name, website_data, actual_url)
            
            # Step 5: Perplexity Gap-Filling (MINIMAL COST - only missing data)
            gap_filled = self._gap_fill_with_perplexity(tool_name, actual_url, xai_enhanced)
            
            # Step 6: Build comprehensive entity
            entity_data = self._build_entity(tool_name, actual_url, website_data, xai_enhanced, gap_filled)
            
            # Step 7: Create or update
            if existing_entity:
                result = self._update_entity(existing_entity['id'], entity_data)
                action = "Updated"
            else:
                result = self._create_entity(entity_data)
                action = "Created"
            
            if result:
                logger.info(f"  ✅ {action} entity: {tool_name}")
                return True
            else:
                logger.error(f"  ❌ Failed to {action.lower()} entity: {tool_name}")
                return False
                
        except Exception as e:
            logger.error(f"  ❌ Error processing {tool_name}: {str(e)}")
            return False
    
    def _resolve_actual_url(self, futuretools_url: str) -> str:
        """Resolve FutureTools redirect to actual website using centralized resolver"""
        try:
            # Use centralized URL resolver for robust redirect handling
            if 'futuretools.link' in futuretools_url or 'futuretools.io' in futuretools_url:
                return self.url_resolver.resolve_futuretools_redirect(futuretools_url)
            else:
                return self.url_resolver.resolve_final_url(futuretools_url)
        except Exception as e:
            logger.warning(f"Error resolving URL: {str(e)}")
            return futuretools_url
    
    def _find_existing_entity(self, name: str, website_url: str) -> dict:
        """Check if entity already exists"""
        try:
            response = requests.get(
                f"{self.client.base_url}/entities",
                headers=self.client._get_headers(),
                params={"search": name, "limit": 10},
                timeout=10
            )
            
            if response.status_code == 200:
                entities = response.json()
                if isinstance(entities, dict) and 'data' in entities:
                    entities = entities['data']
                
                for entity in entities:
                    if (entity.get('name', '').lower() == name.lower() or 
                        entity.get('website_url', '') == website_url):
                        logger.info(f"  🔄 Found existing entity: {entity.get('id')}")
                        return entity
            
            return None
            
        except Exception as e:
            logger.error(f"Error checking existing entity: {str(e)}")
            return None
    
    def _scrape_website(self, url: str) -> dict:
        """Comprehensive website scraping"""
        try:
            response = requests.get(url, timeout=15, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            if response.status_code != 200:
                return {}
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract comprehensive data
            data = {
                'title': self._get_title(soup),
                'meta_description': self._get_meta_description(soup),
                'logo_url': self._extract_logo(soup, url),
                'social_links': self._extract_social_links(response.text),
                'headings': self._extract_headings(soup),
                'paragraphs': self._extract_paragraphs(soup),
                'features_text': self._extract_features_text(soup),
                'pricing_indicators': self._extract_pricing_indicators(soup.get_text()),
                'domain': urlparse(url).netloc
            }
            
            logger.info(f"  📊 Scraped website: {len(data)} data fields")
            return data
            
        except Exception as e:
            logger.warning(f"Error scraping website: {str(e)}")
            return {}
    
    def _enhance_with_xai(self, tool_name: str, website_data: dict, url: str) -> dict:
        """Use XAI for comprehensive enhancement (FREE CREDITS)"""
        try:
            # Create comprehensive prompt for XAI
            content_text = f"""
Tool: {tool_name}
Website: {url}
Title: {website_data.get('title', '')}
Description: {website_data.get('meta_description', '')}
Headings: {', '.join(website_data.get('headings', [])[:5])}
Content: {' '.join(website_data.get('paragraphs', [])[:3])[:1000]}
Features: {website_data.get('features_text', '')}
"""
            
            prompt = f"""
Analyze this tool and extract detailed information. Return ONLY valid JSON:

{content_text}

Required JSON structure:
{{
  "short_description": "Specific 1-sentence description (150 chars max)",
  "description": "Detailed 2-3 sentence description (300-500 chars)", 
  "key_features": ["specific_feature1", "specific_feature2", "specific_feature3", "specific_feature4", "specific_feature5"],
  "use_cases": ["specific_use_case1", "specific_use_case2", "specific_use_case3"],
  "categories": ["relevant_category1", "relevant_category2"],
  "tags": ["relevant_tag1", "relevant_tag2", "relevant_tag3"],
  "pricing_model": "FREE|FREEMIUM|SUBSCRIPTION|PAY_PER_USE|ONE_TIME_PURCHASE|CONTACT_SALES",
  "target_audience": ["specific_audience1", "specific_audience2"],
  "has_free_tier": true/false,
  "api_access": true/false,
  "mobile_support": true/false,
  "integrations": ["integration1", "integration2", "integration3"],
  "supported_os": ["Windows", "macOS", "Linux"],
  "customization_level": "Low|Medium|High",
  "learning_curve": "LOW|MEDIUM|HIGH"
}}

Extract real, specific information from the content. Avoid generic terms.
"""

            response = requests.post(
                "https://api.x.ai/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {self.xai_api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "grok-beta",
                    "messages": [
                        {"role": "system", "content": "You are an expert at analyzing websites and extracting structured data. Always respond with valid JSON."},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.1,
                    "max_tokens": 2000  # XAI is free, so we can be generous
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                # Extract JSON
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    enhanced_data = json.loads(content[json_start:json_end])
                    logger.info(f"  🤖 XAI enhanced {tool_name} (FREE)")
                    return enhanced_data
            
            logger.warning(f"  ⚠️ XAI enhancement failed for {tool_name}")
            return {}
            
        except Exception as e:
            logger.error(f"  ❌ XAI error: {str(e)}")
            return {}
    
    def _gap_fill_with_perplexity(self, tool_name: str, url: str, xai_data: dict) -> dict:
        """Use Perplexity for MINIMAL gap filling (COST OPTIMIZED)"""
        try:
            # Only use Perplexity for specific factual data gaps
            prompt = f"""
Research ONLY missing factual data for "{tool_name}" (website: {url}).

Return ONLY these fields in JSON (skip if uncertain):
{{
  "founded_year": 2023,
  "employee_count_range": "C1_10|C11_50|C51_200|C201_500|C501_1000|C1001_5000|C5001_PLUS",
  "funding_stage": "PRE_SEED|SEED|SERIES_A|SERIES_B|SERIES_C|SERIES_D_PLUS|PUBLIC",
  "location_summary": "City, Country"
}}

Be brief and factual only.
"""

            response = requests.post(
                "https://api.perplexity.ai/chat/completions",
                headers={
                    "Authorization": f"Bearer {self.perplexity_api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "llama-3.1-sonar-small-128k-online",  # CHEAPEST
                    "messages": [
                        {"role": "system", "content": "Provide only verified factual data in JSON format. Be concise."},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.1,
                    "max_tokens": 300  # MINIMAL TOKENS
                },
                timeout=20
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    gap_data = json.loads(content[json_start:json_end])
                    logger.info(f"  💰 Perplexity gap-filled {tool_name} (MINIMAL COST)")
                    return gap_data
            
            logger.warning(f"  ⚠️ Perplexity gap-filling failed for {tool_name}")
            return {}
            
        except Exception as e:
            logger.warning(f"  ⚠️ Perplexity error (acceptable): {str(e)}")
            return {}
    
    def _build_entity(self, name: str, url: str, website_data: dict, xai_data: dict, gap_data: dict) -> dict:
        """Build comprehensive entity from all data sources"""
        
        # Get entity type
        entity_type = self.entity_detector.detect_entity_type(name, url)
        entity_type_id = self._get_entity_type_id(entity_type)
        
        # Merge data intelligently
        merged_data = {}
        for key in ['short_description', 'description', 'key_features', 'use_cases', 
                   'categories', 'tags', 'pricing_model', 'target_audience']:
            merged_data[key] = xai_data.get(key, [])
        
        # Add gap-filled data
        merged_data.update(gap_data)
        
        # Add website data
        merged_data['logo_url'] = website_data.get('logo_url')
        merged_data['social_links'] = website_data.get('social_links', {})
        
        # Map taxonomy
        category_ids = self.taxonomy_service.map_categories(merged_data.get('categories', []))
        tag_ids = self.taxonomy_service.map_tags(merged_data.get('tags', []))
        feature_ids = self.taxonomy_service.map_features(merged_data.get('key_features', []))
        
        # Ensure at least one category
        if not category_ids and self.taxonomy_service.categories_map:
            first_cat = list(self.taxonomy_service.categories_map.values())[0]
            category_ids = [first_cat['id']]
        
        # Build entity
        entity = {
            'name': name,
            'website_url': url,
            'entity_type_id': entity_type_id,
            'short_description': merged_data.get('short_description', f"{name} - Advanced AI-powered solution")[:150],
            'description': merged_data.get('description', f"{name} provides comprehensive tools and solutions.")[:500],
            'category_ids': category_ids,
            'tag_ids': tag_ids,
            'feature_ids': feature_ids,
            'meta_title': f"{name} | AI Navigator",
            'meta_description': merged_data.get('short_description', '')[:160],
            'ref_link': url,
            'affiliate_status': 'NONE',
            'status': 'ACTIVE'
        }
        
        # Add optional fields
        optional_fields = ['logo_url', 'founded_year', 'employee_count_range', 'funding_stage', 'location_summary', 'social_links']
        for field in optional_fields:
            if merged_data.get(field):
                entity[field] = merged_data[field]
        
        # Add tool details
        entity['tool_details'] = {
            'learning_curve': merged_data.get('learning_curve', 'MEDIUM'),
            'key_features': merged_data.get('key_features', [])[:10],
            'has_free_tier': merged_data.get('has_free_tier', True),
            'use_cases': merged_data.get('use_cases', [])[:5],
            'pricing_model': merged_data.get('pricing_model', 'FREEMIUM'),
            'target_audience': merged_data.get('target_audience', [])[:5],
            'mobile_support': merged_data.get('mobile_support', False),
            'api_access': merged_data.get('api_access', False),
            'customization_level': merged_data.get('customization_level', 'Medium'),
            'trial_available': True,
            'demo_available': False,
            'open_source': False,
            'support_channels': ['Email', 'Documentation'],
            'integrations': merged_data.get('integrations', [])[:8],
            'supported_os': merged_data.get('supported_os', [])
        }
        
        return entity
    
    def _get_entity_type_id(self, entity_type_slug: str) -> str:
        """Get entity type ID from API"""
        try:
            response = requests.get(
                f"{self.client.base_url}/entity-types",
                headers=self.client._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                entity_types = response.json()
                for et in entity_types:
                    if et.get('slug') == entity_type_slug:
                        return et.get('id')
            
            return "fd181400-c9e6-431c-a8bd-c068d0491aba"  # ai-tool fallback
            
        except Exception as e:
            logger.error(f"Error getting entity type ID: {str(e)}")
            return "fd181400-c9e6-431c-a8bd-c068d0491aba"
    
    def _create_entity(self, entity_data: dict) -> bool:
        """Create new entity"""
        try:
            result = self.client.create_entity(entity_data)
            return bool(result)
        except Exception as e:
            logger.error(f"Error creating entity: {str(e)}")
            return False
    
    def _update_entity(self, entity_id: str, entity_data: dict) -> bool:
        """Update existing entity"""
        try:
            update_data = entity_data.copy()
            update_data.pop('name', None)  # Don't change name

            # CRITICAL: Ensure status is ACTIVE for all updates
            update_data["status"] = "ACTIVE"

            response = requests.patch(
                f"{self.client.base_url}/entities/{entity_id}",
                headers=self.client._get_headers(),
                json=update_data,
                timeout=30
            )
            
            return response.status_code in [200, 201]
        except Exception as e:
            logger.error(f"Error updating entity: {str(e)}")
            return False
    
    # Helper methods for scraping
    def _get_title(self, soup):
        title = soup.find('title')
        return title.get_text().strip() if title else ""
    
    def _get_meta_description(self, soup):
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        return meta_desc.get('content', '').strip() if meta_desc else ""
    
    def _extract_logo(self, soup, base_url):
        selectors = [
            'img[alt*="logo" i]', '.logo img', 'header img', '.navbar img',
            'img[src*="logo" i]', 'link[rel="apple-touch-icon"]', 'link[rel="icon"]'
        ]
        
        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                src = element.get('src') or element.get('href')
                if src:
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = urljoin(base_url, src)
                    if src.startswith('http'):
                        return src
        
        # Fallback
        domain = urlparse(base_url).netloc
        return f"https://www.google.com/s2/favicons?sz=128&domain={domain}"
    
    def _extract_social_links(self, page_text):
        patterns = {
            'twitter': r'twitter\.com/([a-zA-Z0-9_]{1,15})',
            'linkedin': r'linkedin\.com/company/([a-zA-Z0-9\-]{1,50})',
            'github': r'github\.com/([a-zA-Z0-9\-_]{1,39})'
        }
        
        social_links = {}
        for platform, pattern in patterns.items():
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                handle = matches[0]
                if handle and not any(skip in handle.lower() for skip in ['home', 'login', 'about']):
                    social_links[platform] = handle
        
        return social_links
    
    def _extract_headings(self, soup):
        headings = []
        for h in soup.find_all(['h1', 'h2', 'h3']):
            text = h.get_text().strip()
            if text and 5 < len(text) < 100:
                headings.append(text)
        return headings[:10]
    
    def _extract_paragraphs(self, soup):
        paragraphs = []
        for p in soup.find_all('p'):
            text = p.get_text().strip()
            if text and 50 < len(text) < 500:
                paragraphs.append(text)
        return paragraphs[:5]
    
    def _extract_features_text(self, soup):
        # Look for feature sections
        feature_text = ""
        feature_sections = soup.find_all(text=re.compile(r'features|capabilities|what we do', re.IGNORECASE))
        for section in feature_sections[:2]:
            parent = section.parent
            if parent:
                feature_text += parent.get_text()[:500]
        return feature_text
    
    def _extract_pricing_indicators(self, text):
        text_lower = text.lower()
        indicators = {}
        
        if any(word in text_lower for word in ['free', 'no cost', '$0']):
            indicators['has_free_tier'] = True
        
        if 'subscription' in text_lower or 'monthly' in text_lower:
            indicators['pricing_model'] = 'SUBSCRIPTION'
        elif 'pay per' in text_lower:
            indicators['pricing_model'] = 'PAY_PER_USE'
        
        return indicators

def main():
    """Test the cost-optimized processor"""
    
    # Use configuration from environment variables
    processor = CostOptimizedProcessor(config.api.xai_api_key, config.api.perplexity_api_key)
    
    # Load tools
    tools_file = '/app/ai-navigator-scrapers/futuretools_leads.jsonl'
    with open(tools_file, 'r') as f:
        tools = [json.loads(line.strip()) for line in f]
    
    # Process first 5 tools for testing
    logger.info(f"🚀 Processing 5 tools with cost optimization...")
    logger.info(f"💰 Estimated cost: $0.10-0.25 total")
    
    success_count = 0
    for i, tool in enumerate(tools[20:25]):  # Skip already processed ones
        logger.info(f"\n{'='*60}")
        logger.info(f"Processing tool {i+1}/5")
        
        success = processor.process_tool(tool)
        if success:
            success_count += 1
        
        time.sleep(2)  # Be respectful
    
    logger.info(f"\n🎉 SUCCESS RATE: {success_count}/5 tools ({(success_count/5)*100:.1f}%)")
    logger.info(f"💰 Estimated cost: ${success_count * 0.05:.2f}")

if __name__ == "__main__":
    main()