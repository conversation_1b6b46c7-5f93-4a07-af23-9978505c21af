<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Navigator - Bulk File Upload</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f7fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #2c3e50; margin-bottom: 10px; }
        .header p { color: #7f8c8d; font-size: 16px; }
        
        .upload-section { background: white; border-radius: 12px; padding: 30px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .upload-area { border: 2px dashed #3498db; border-radius: 8px; padding: 40px; text-align: center; margin-bottom: 20px; transition: all 0.3s; }
        .upload-area:hover { border-color: #2980b9; background: #f8f9fa; }
        .upload-area.dragover { border-color: #27ae60; background: #e8f5e8; }
        
        .file-input { display: none; }
        .upload-btn { background: #3498db; color: white; padding: 12px 24px; border: none; border-radius: 6px; cursor: pointer; font-size: 16px; margin: 10px; }
        .upload-btn:hover { background: #2980b9; }
        
        .options { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px; }
        .option { display: flex; align-items: center; gap: 8px; }
        .option input[type="checkbox"] { width: 18px; height: 18px; }
        
        .process-btn { background: #27ae60; color: white; padding: 15px 30px; border: none; border-radius: 8px; font-size: 18px; cursor: pointer; width: 100%; }
        .process-btn:hover { background: #229954; }
        .process-btn:disabled { background: #95a5a6; cursor: not-allowed; }
        
        .status-section { background: white; border-radius: 12px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .job-card { background: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 15px; border-left: 4px solid #3498db; }
        .job-header { display: flex; justify-content: between; align-items: center; margin-bottom: 10px; }
        .job-id { font-family: monospace; background: #ecf0f1; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
        .progress-bar { background: #ecf0f1; border-radius: 10px; height: 20px; margin: 10px 0; overflow: hidden; }
        .progress-fill { background: #3498db; height: 100%; transition: width 0.3s; border-radius: 10px; }
        .status { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; text-transform: uppercase; }
        .status.running { background: #f39c12; color: white; }
        .status.completed { background: #27ae60; color: white; }
        .status.failed { background: #e74c3c; color: white; }
        
        .file-info { background: #e8f4fd; padding: 15px; border-radius: 6px; margin-bottom: 20px; }
        .sample-format { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin-bottom: 20px; }
        .sample-format h4 { color: #856404; margin-bottom: 10px; }
        .sample-table { width: 100%; border-collapse: collapse; font-size: 14px; }
        .sample-table th, .sample-table td { padding: 8px 12px; border: 1px solid #ddd; text-align: left; }
        .sample-table th { background: #f8f9fa; font-weight: bold; }
        
        .log { background: #2c3e50; color: #ecf0f1; padding: 20px; border-radius: 8px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto; margin-top: 20px; }
        .log-entry { margin-bottom: 5px; }
        .log-entry.success { color: #2ecc71; }
        .log-entry.error { color: #e74c3c; }
        .log-entry.info { color: #3498db; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 AI Navigator - Bulk File Upload</h1>
            <p>Upload CSV or Excel files with tool names and URLs for AI-powered enhancement and database integration</p>
        </div>

        <div class="upload-section">
            <h2>📁 File Upload</h2>
            
            <div class="sample-format">
                <h4>📋 Required File Format</h4>
                <p>Your CSV/Excel file must contain these columns:</p>
                <table class="sample-table">
                    <thead>
                        <tr><th>name</th><th>url</th></tr>
                    </thead>
                    <tbody>
                        <tr><td>ChatGPT</td><td>https://chat.openai.com</td></tr>
                        <tr><td>Claude</td><td>https://claude.ai</td></tr>
                        <tr><td>Midjourney</td><td>https://midjourney.com</td></tr>
                    </tbody>
                </table>
            </div>

            <div class="upload-area" id="uploadArea">
                <div>
                    <h3>📤 Drop your CSV/Excel file here</h3>
                    <p>or click to browse files</p>
                    <button class="upload-btn" onclick="document.getElementById('fileInput').click()">Choose File</button>
                    <input type="file" id="fileInput" class="file-input" accept=".csv,.xlsx,.xls" onchange="handleFileSelect(event)">
                </div>
            </div>

            <div id="fileInfo" class="file-info" style="display: none;">
                <h4>📄 Selected File</h4>
                <p id="fileName"></p>
                <p id="fileSize"></p>
            </div>

            <div class="options">
                <div class="option">
                    <input type="checkbox" id="useParallel" checked>
                    <label for="useParallel">⚡ Use Parallel Processing (faster)</label>
                </div>
                <div class="option">
                    <input type="checkbox" id="usePhase3" checked>
                    <label for="usePhase3">🧠 Enable Phase 3 Advanced Analysis</label>
                </div>
            </div>

            <button class="process-btn" id="processBtn" onclick="processFile()" disabled>
                🚀 Start Processing
            </button>
        </div>

        <div class="status-section">
            <h2>📊 Processing Status</h2>
            <div id="jobsList"></div>
            <div id="logs" class="log" style="display: none;"></div>
        </div>
    </div>

    <script>
        let selectedFile = null;
        let currentJobs = {};
        const API_BASE_URL = 'http://localhost:8001';

        // File upload handling
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const processBtn = document.getElementById('processBtn');

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            const validTypes = ['text/csv', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
            const validExtensions = ['.csv', '.xls', '.xlsx'];
            
            const isValidType = validTypes.includes(file.type) || validExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
            
            if (!isValidType) {
                alert('Please select a CSV or Excel file (.csv, .xls, .xlsx)');
                return;
            }

            selectedFile = file;
            document.getElementById('fileName').textContent = `Name: ${file.name}`;
            document.getElementById('fileSize').textContent = `Size: ${(file.size / 1024).toFixed(1)} KB`;
            fileInfo.style.display = 'block';
            processBtn.disabled = false;
            
            log(`📁 File selected: ${file.name} (${(file.size / 1024).toFixed(1)} KB)`, 'info');
        }

        async function processFile() {
            if (!selectedFile) {
                alert('Please select a file first');
                return;
            }

            const useParallel = document.getElementById('useParallel').checked;
            const usePhase3 = document.getElementById('usePhase3').checked;

            const formData = new FormData();
            formData.append('file', selectedFile);
            formData.append('use_parallel', useParallel);
            formData.append('use_phase3', usePhase3);

            processBtn.disabled = true;
            processBtn.textContent = '⏳ Processing...';

            try {
                log(`🚀 Starting file processing: ${selectedFile.name}`, 'info');
                log(`⚙️ Settings: Parallel=${useParallel}, Phase3=${usePhase3}`, 'info');

                const response = await fetch(`${API_BASE_URL}/api/process-file-upload`, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    log(`✅ Job created successfully: ${data.job_id}`, 'success');
                    log(`📊 Processing ${data.total_tools} tools from ${data.filename}`, 'info');
                    
                    currentJobs[data.job_id] = {
                        ...data,
                        startTime: Date.now()
                    };
                    
                    updateJobDisplay();
                    startPolling(data.job_id);
                } else {
                    log(`❌ Error: ${data.error}`, 'error');
                }
            } catch (error) {
                log(`❌ Network error: ${error.message}`, 'error');
            } finally {
                processBtn.disabled = false;
                processBtn.textContent = '🚀 Start Processing';
            }
        }

        async function startPolling(jobId) {
            const pollInterval = setInterval(async () => {
                try {
                    const response = await fetch(`${API_BASE_URL}/api/job-status/${jobId}`);
                    const data = await response.json();
                    
                    currentJobs[jobId] = { ...currentJobs[jobId], ...data };
                    updateJobDisplay();
                    
                    if (data.status === 'completed' || data.status === 'failed') {
                        clearInterval(pollInterval);
                        log(`🏁 Job ${jobId} finished with status: ${data.status}`, data.status === 'completed' ? 'success' : 'error');
                    }
                } catch (error) {
                    log(`❌ Error polling job ${jobId}: ${error.message}`, 'error');
                }
            }, 2000);
        }

        function updateJobDisplay() {
            const jobsList = document.getElementById('jobsList');
            jobsList.innerHTML = '';
            
            Object.entries(currentJobs).forEach(([jobId, job]) => {
                const jobCard = document.createElement('div');
                jobCard.className = 'job-card';
                
                const progress = job.progress || 0;
                const statusClass = job.status || 'running';
                
                jobCard.innerHTML = `
                    <div class="job-header">
                        <div>
                            <strong>📁 ${job.filename || 'File Upload'}</strong>
                            <span class="job-id">${jobId}</span>
                        </div>
                        <span class="status ${statusClass}">${statusClass}</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${progress}%"></div>
                    </div>
                    <div>
                        <small>Progress: ${progress.toFixed(1)}% | Tools: ${job.total_tools || 0} | Mode: ${job.processing_mode || 'sequential'}</small>
                    </div>
                `;
                
                jobsList.appendChild(jobCard);
            });
        }

        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            logs.style.display = 'block';
            
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logs.appendChild(logEntry);
            logs.scrollTop = logs.scrollHeight;
        }

        // Initialize
        log('🌐 AI Navigator Bulk Upload Interface Ready', 'info');
        log(`🔗 Connected to: ${API_BASE_URL}`, 'info');
    </script>
</body>
</html>
