"""
Shared Utilities
Common utility functions used across the project
"""

import re
import logging
from typing import Dict, List, Optional, Any
from urllib.parse import urlparse
import time
import random

def setup_logging(name: str, level: str = "INFO") -> logging.Logger:
    """Setup standardized logging"""
    logger = logging.getLogger(name)

    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(getattr(logging, level.upper()))

    return logger

def clean_url(url: str) -> str:
    """Clean and normalize URL"""
    if not url:
        return ""

    url = url.strip()

    # Add protocol if missing
    if not url.startswith(('http://', 'https://')):
        url = 'https://' + url

    # Remove trailing slash
    url = url.rstrip('/')

    return url

def extract_domain_info(url: str) -> Dict[str, str]:
    """Extract domain information from URL"""
    try:
        parsed = urlparse(url)
        domain = parsed.netloc.lower()

        # Remove www prefix
        if domain.startswith('www.'):
            domain = domain[4:]

        # Extract company name from domain
        company_name = domain.split('.')[0]
        company_name = company_name.replace('-', ' ').replace('_', ' ').title()

        return {
            'domain': domain,
            'company_name': company_name,
            'tld': parsed.netloc.split('.')[-1] if '.' in parsed.netloc else ''
        }
    except Exception:
        return {'domain': '', 'company_name': '', 'tld': ''}

def normalize_text(text: str, max_length: Optional[int] = None) -> str:
    """Normalize text content"""
    if not text:
        return ""

    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text.strip())

    # Remove special characters but keep basic punctuation
    text = re.sub(r'[^\w\s\-\.\,\!\?\:\;\(\)\/\&\@\#\$\%\+\=]', '', text)

    # Truncate if needed - prefer sentence boundaries
    if max_length and len(text) > max_length:
        # Try to break at sentence boundary first
        truncated = text[:max_length - 3]
        last_period = truncated.rfind('. ')
        if last_period > max_length * 0.7:
            text = truncated[:last_period + 1].strip()
        else:
            # Fall back to word boundary
            text = text[:max_length].rsplit(' ', 1)[0] + '...'

    return text

def extract_keywords(text: str, min_length: int = 3) -> List[str]:
    """Extract keywords from text"""
    if not text:
        return []

    # Convert to lowercase and split
    words = re.findall(r'\b[a-zA-Z]{' + str(min_length) + ',}\b', text.lower())

    # Remove common stop words
    stop_words = {
        'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had',
        'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his',
        'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy',
        'did', 'she', 'use', 'way', 'will', 'with', 'this', 'that', 'have',
        'from', 'they', 'know', 'want', 'been', 'good', 'much', 'some', 'time',
        'very', 'when', 'come', 'here', 'just', 'like', 'long', 'make', 'many',
        'over', 'such', 'take', 'than', 'them', 'well', 'were'
    }

    keywords = [word for word in words if word not in stop_words]

    # Remove duplicates while preserving order
    seen = set()
    unique_keywords = []
    for keyword in keywords:
        if keyword not in seen:
            seen.add(keyword)
            unique_keywords.append(keyword)

    return unique_keywords[:20]  # Limit to top 20

def safe_get_nested(data: Dict[str, Any], path: str, default: Any = None) -> Any:
    """Safely get nested dictionary value using dot notation"""
    try:
        keys = path.split('.')
        value = data
        for key in keys:
            value = value[key]
        return value
    except (KeyError, TypeError):
        return default

def rate_limit_delay(min_delay: float = 1.0, max_delay: float = 3.0) -> None:
    """Apply rate limiting delay"""
    delay = random.uniform(min_delay, max_delay)
    time.sleep(delay)

def validate_email(email: str) -> bool:
    """Validate email format"""
    if not email:
        return False

    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def validate_url(url: str) -> bool:
    """Validate URL format"""
    if not url:
        return False

    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False

def truncate_text(text: str, max_length: int, suffix: str = "...") -> str:
    """Truncate text to maximum length, preferring sentence boundaries"""
    if not text or len(text) <= max_length:
        return text

    # First try to break at sentence boundary
    truncated = text[:max_length - len(suffix)]
    last_period = truncated.rfind('. ')

    if last_period > max_length * 0.7:  # If we can break at a sentence
        return truncated[:last_period + 1].strip()

    # Otherwise try to break at word boundary
    last_space = truncated.rfind(' ')
    if last_space > max_length * 0.8:  # If we can break at a reasonable point
        truncated = truncated[:last_space]

    return truncated + suffix