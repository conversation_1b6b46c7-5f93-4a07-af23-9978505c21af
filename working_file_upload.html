<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Navigator Scrapers - File Upload</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .upload-section { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .file-drop-zone { border: 2px dashed #007bff; border-radius: 8px; padding: 40px; text-align: center; margin: 20px 0; transition: all 0.3s; }
        .file-drop-zone:hover { border-color: #0056b3; background: #f8f9ff; }
        .file-drop-zone.dragover { border-color: #28a745; background: #f8fff8; }
        .file-selected { border-color: #28a745; background: #f8fff8; }
        .btn { padding: 12px 24px; margin: 10px; border: none; border-radius: 6px; cursor: pointer; font-size: 16px; transition: all 0.3s; }
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; }
        .btn-success { background: #28a745; color: white; }
        .btn-success:hover { background: #1e7e34; }
        .btn:disabled { background: #6c757d; cursor: not-allowed; }
        .checkbox-group { margin: 15px 0; }
        .checkbox-group label { display: flex; align-items: center; margin: 8px 0; }
        .checkbox-group input[type="checkbox"] { margin-right: 10px; }
        .status { padding: 15px; border-radius: 6px; margin: 15px 0; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .requirements { background: #e7f3ff; padding: 15px; border-radius: 6px; margin: 15px 0; }
        .requirements h4 { margin-top: 0; color: #0066cc; }
        .requirements ul { margin: 10px 0; padding-left: 20px; }
        .hidden { display: none; }
        .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .progress-fill { height: 100%; background: #007bff; transition: width 0.3s; }
        .job-status { background: #f8f9fa; padding: 15px; border-radius: 6px; margin: 15px 0; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 AI Navigator Scrapers - File Upload</h1>
        
        <div class="upload-section">
            <h3>📁 Upload CSV/Excel File</h3>
            <p>Upload a file with 'name' and 'url' columns to process multiple tools through the AI enhancement pipeline.</p>
            
            <div class="requirements">
                <h4>📋 File Requirements:</h4>
                <ul>
                    <li>Must have 'name' and 'url' columns</li>
                    <li>Supported formats: CSV, Excel (.xlsx, .xls)</li>
                    <li>Each row represents one tool to process</li>
                    <li>Empty rows will be skipped</li>
                    <li>Example: name: "ChatGPT", url: "https://chat.openai.com"</li>
                </ul>
            </div>

            <div id="fileDropZone" class="file-drop-zone">
                <div id="dropText">
                    <p>📎 Drop your CSV/Excel file here, or <button type="button" class="btn btn-primary" onclick="document.getElementById('fileInput').click()">Browse Files</button></p>
                    <p style="font-size: 14px; color: #666;">Supported: .csv, .xlsx, .xls</p>
                </div>
                <div id="fileInfo" class="hidden">
                    <p><strong>Selected:</strong> <span id="fileName"></span></p>
                    <p><strong>Size:</strong> <span id="fileSize"></span></p>
                    <button type="button" class="btn btn-primary" onclick="clearFile()">Remove File</button>
                </div>
            </div>
            
            <input type="file" id="fileInput" accept=".csv,.xlsx,.xls" style="display: none;">
            
            <div class="checkbox-group">
                <label>
                    <input type="checkbox" id="useParallel" checked>
                    Use Parallel Processing (faster)
                </label>
                <label>
                    <input type="checkbox" id="usePhase3" checked>
                    Enable Phase 3 Advanced Analysis
                </label>
            </div>
            
            <button id="uploadBtn" class="btn btn-success" onclick="uploadFile()" disabled>
                🚀 Process File Upload
            </button>
            
            <div id="status"></div>
            <div id="jobStatus" class="hidden"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8001';
        let selectedFile = null;
        let currentJobId = null;
        let pollInterval = null;

        // File input handling
        document.getElementById('fileInput').addEventListener('change', handleFileSelect);
        
        // Drag and drop handling
        const dropZone = document.getElementById('fileDropZone');
        dropZone.addEventListener('dragover', handleDragOver);
        dropZone.addEventListener('dragleave', handleDragLeave);
        dropZone.addEventListener('drop', handleDrop);

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                setSelectedFile(file);
            }
        }

        function handleDragOver(event) {
            event.preventDefault();
            dropZone.classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.preventDefault();
            dropZone.classList.remove('dragover');
        }

        function handleDrop(event) {
            event.preventDefault();
            dropZone.classList.remove('dragover');
            
            const files = event.dataTransfer.files;
            if (files.length > 0) {
                setSelectedFile(files[0]);
            }
        }

        function setSelectedFile(file) {
            selectedFile = file;
            document.getElementById('dropText').classList.add('hidden');
            document.getElementById('fileInfo').classList.remove('hidden');
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);
            document.getElementById('uploadBtn').disabled = false;
            dropZone.classList.add('file-selected');
        }

        function clearFile() {
            selectedFile = null;
            document.getElementById('fileInput').value = '';
            document.getElementById('dropText').classList.remove('hidden');
            document.getElementById('fileInfo').classList.add('hidden');
            document.getElementById('uploadBtn').disabled = true;
            dropZone.classList.remove('file-selected');
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        async function uploadFile() {
            if (!selectedFile) {
                showStatus('Please select a file first.', 'error');
                return;
            }

            const uploadBtn = document.getElementById('uploadBtn');
            uploadBtn.disabled = true;
            uploadBtn.textContent = '⏳ Processing...';

            try {
                const formData = new FormData();
                formData.append('file', selectedFile);
                formData.append('use_parallel', document.getElementById('useParallel').checked);
                formData.append('use_phase3', document.getElementById('usePhase3').checked);

                showStatus('📤 Uploading file and starting processing...', 'info');

                const response = await fetch(`${API_BASE_URL}/api/process-file-upload`, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    currentJobId = data.job_id;
                    showStatus(`✅ Upload successful! Processing ${data.total_tools} tools.`, 'success');
                    showJobStatus(data);
                    startPolling();
                } else {
                    showStatus(`❌ Upload failed: ${data.error || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showStatus(`❌ Network error: ${error.message}`, 'error');
            } finally {
                uploadBtn.disabled = false;
                uploadBtn.textContent = '🚀 Process File Upload';
            }
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }

        function showJobStatus(jobData) {
            const jobStatusDiv = document.getElementById('jobStatus');
            jobStatusDiv.classList.remove('hidden');
            jobStatusDiv.innerHTML = `
                <div class="job-status">
                    <h4>📊 Job Status: ${jobData.job_id}</h4>
                    <p><strong>File:</strong> ${jobData.filename}</p>
                    <p><strong>Tools:</strong> ${jobData.total_tools}</p>
                    <p><strong>Parallel:</strong> ${jobData.parallel_enabled ? 'Yes' : 'No'}</p>
                    <p><strong>Phase 3:</strong> ${jobData.phase3_enabled ? 'Yes' : 'No'}</p>
                    <div class="progress-bar">
                        <div id="progressFill" class="progress-fill" style="width: 0%"></div>
                    </div>
                    <p id="progressText">Starting...</p>
                </div>
            `;
        }

        async function startPolling() {
            if (!currentJobId) return;

            pollInterval = setInterval(async () => {
                try {
                    const response = await fetch(`${API_BASE_URL}/api/job-status/${currentJobId}`);
                    const data = await response.json();

                    if (response.ok) {
                        updateJobProgress(data);
                        
                        if (data.status === 'completed' || data.status === 'failed') {
                            clearInterval(pollInterval);
                            pollInterval = null;
                        }
                    }
                } catch (error) {
                    console.error('Polling error:', error);
                }
            }, 5000); // Poll every 5 seconds
        }

        function updateJobProgress(data) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            
            if (progressFill && progressText) {
                const progress = data.progress || 0;
                progressFill.style.width = `${progress}%`;
                progressText.textContent = `${data.status} - ${progress.toFixed(1)}% complete`;
                
                if (data.status === 'completed') {
                    showStatus('🎉 File processing completed successfully!', 'success');
                } else if (data.status === 'failed') {
                    showStatus('❌ File processing failed. Check logs for details.', 'error');
                }
            }
        }

        // Initialize
        console.log('🌐 File Upload Interface Ready');
        console.log(`🔗 Backend URL: ${API_BASE_URL}`);
    </script>
</body>
</html>
