"""
Pagination State Service for AI Navigator Scrapers

This service tracks pagination state for each spider to enable resuming
scraping from the last processed page instead of starting from page 1.
"""

import os
import json
import logging
import threading
from datetime import datetime
from typing import Dict, Optional, Any
from dataclasses import dataclass, asdict
from config import config


@dataclass
class PaginationState:
    """Represents pagination state for a spider"""
    spider_name: str
    last_page_scraped: int
    total_pages_discovered: Optional[int]
    last_updated: str
    total_items_scraped: int
    last_successful_run: str
    notes: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PaginationState':
        """Create from dictionary"""
        return cls(**data)


class PaginationStateService:
    """Service for managing pagination state across scraping sessions"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.state_dir = config.pagination_state_dir
        self.state_file = os.path.join(self.state_dir, "pagination_state.json")
        self._lock = threading.Lock()
        
        # Ensure state directory exists
        os.makedirs(self.state_dir, exist_ok=True)
        
        # Load existing state
        self._state_cache = self._load_state()
        
        self.logger.info(f"Pagination state service initialized. State file: {self.state_file}")
    
    def _load_state(self) -> Dict[str, PaginationState]:
        """Load pagination state from file"""
        if not os.path.exists(self.state_file):
            self.logger.info("No existing pagination state file found. Starting fresh.")
            return {}
        
        try:
            with open(self.state_file, 'r') as f:
                data = json.load(f)
            
            state_cache = {}
            for spider_name, state_data in data.items():
                try:
                    state_cache[spider_name] = PaginationState.from_dict(state_data)
                except Exception as e:
                    self.logger.warning(f"Failed to load state for spider {spider_name}: {e}")
            
            self.logger.info(f"Loaded pagination state for {len(state_cache)} spiders")
            return state_cache
            
        except Exception as e:
            self.logger.error(f"Failed to load pagination state: {e}")
            return {}
    
    def _save_state(self):
        """Save pagination state to file"""
        try:
            # Convert state cache to serializable format
            data = {}
            for spider_name, state in self._state_cache.items():
                data[spider_name] = state.to_dict()
            
            # Write to temporary file first, then rename for atomic operation
            temp_file = self.state_file + ".tmp"
            with open(temp_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            # Atomic rename
            os.rename(temp_file, self.state_file)
            self.logger.debug(f"Saved pagination state for {len(data)} spiders")
            
        except Exception as e:
            self.logger.error(f"Failed to save pagination state: {e}")
    
    def get_spider_state(self, spider_name: str) -> Optional[PaginationState]:
        """Get pagination state for a specific spider"""
        with self._lock:
            return self._state_cache.get(spider_name)
    
    def get_start_page(self, spider_name: str, default_start_page: int = 1) -> int:
        """Get the starting page for a spider (last scraped page + 1)"""
        state = self.get_spider_state(spider_name)
        if state and state.last_page_scraped > 0:
            start_page = state.last_page_scraped + 1
            self.logger.info(f"Spider {spider_name} will resume from page {start_page}")
            return start_page
        else:
            self.logger.info(f"Spider {spider_name} will start from page {default_start_page} (no previous state)")
            return default_start_page
    
    def update_spider_state(self, spider_name: str, last_page_scraped: int, 
                          items_scraped: int = 0, total_pages: Optional[int] = None,
                          notes: str = "") -> None:
        """Update pagination state for a spider"""
        with self._lock:
            current_time = datetime.now().isoformat()
            
            if spider_name in self._state_cache:
                # Update existing state
                state = self._state_cache[spider_name]
                state.last_page_scraped = max(state.last_page_scraped, last_page_scraped)
                state.total_items_scraped += items_scraped
                state.last_updated = current_time
                if total_pages:
                    state.total_pages_discovered = total_pages
                if items_scraped > 0:
                    state.last_successful_run = current_time
                if notes:
                    state.notes = notes
            else:
                # Create new state
                self._state_cache[spider_name] = PaginationState(
                    spider_name=spider_name,
                    last_page_scraped=last_page_scraped,
                    total_pages_discovered=total_pages,
                    last_updated=current_time,
                    total_items_scraped=items_scraped,
                    last_successful_run=current_time if items_scraped > 0 else "",
                    notes=notes
                )
            
            # Save to file
            self._save_state()
            
            self.logger.info(f"Updated pagination state for {spider_name}: page {last_page_scraped}, {items_scraped} items")
    
    def reset_spider_state(self, spider_name: str) -> bool:
        """Reset pagination state for a spider"""
        with self._lock:
            if spider_name in self._state_cache:
                del self._state_cache[spider_name]
                self._save_state()
                self.logger.info(f"Reset pagination state for spider {spider_name}")
                return True
            return False
    
    def get_all_states(self) -> Dict[str, PaginationState]:
        """Get all pagination states"""
        with self._lock:
            return self._state_cache.copy()
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of pagination states"""
        with self._lock:
            summary = {
                "total_spiders": len(self._state_cache),
                "spiders": {}
            }
            
            for spider_name, state in self._state_cache.items():
                summary["spiders"][spider_name] = {
                    "last_page": state.last_page_scraped,
                    "total_items": state.total_items_scraped,
                    "last_updated": state.last_updated,
                    "next_start_page": state.last_page_scraped + 1
                }
            
            return summary


# Global pagination state service instance
pagination_service = PaginationStateService()
