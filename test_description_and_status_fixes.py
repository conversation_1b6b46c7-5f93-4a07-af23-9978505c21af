#!/usr/bin/env python3
"""
Test script to verify both critical fixes:
1. Descriptions are now much longer (1500-2500 chars instead of 400-600)
2. Status: ACTIVE is included in ALL POST/PATCH requests
"""

import sys
import os
import json
import logging

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_description_length_fixes():
    """Test that description length limits have been increased"""
    
    print("🔍 TESTING DESCRIPTION LENGTH FIXES")
    print("=" * 50)
    
    # Test 1: Check type_specific_enhancers.py prompts
    try:
        from type_specific_enhancers import AIToolEnhancer
        
        enhancer = AIToolEnhancer()
        prompt = enhancer.get_enhancement_prompt("Test Tool", "https://test.com")
        
        print("✅ AI Tool Enhancer Prompt Check:")
        if "1500-2500 character" in prompt:
            print("   ✅ FIXED: Prompt now asks for 1500-2500 character descriptions")
        else:
            print("   ❌ ISSUE: Prompt still has old length requirements")
            
        if "EXTREMELY DETAILED" in prompt:
            print("   ✅ FIXED: Prompt emphasizes detailed descriptions")
        else:
            print("   ❌ ISSUE: Prompt doesn't emphasize detail")
            
    except Exception as e:
        print(f"   ❌ Error testing enhancer: {e}")
    
    # Test 2: Check advanced_data_extractor.py limits
    print("\n✅ Advanced Data Extractor Length Check:")
    try:
        with open('advanced_data_extractor.py', 'r') as f:
            content = f.read()
            
        if "[:2497]" in content:
            print("   ✅ FIXED: Description limit increased to 2500 chars")
        else:
            print("   ❌ ISSUE: Description limit still too low")
            
    except Exception as e:
        print(f"   ❌ Error checking file: {e}")
    
    # Test 3: Check enhanced_item_processor.py limits
    print("\n✅ Enhanced Item Processor Length Check:")
    try:
        with open('enhanced_item_processor.py', 'r') as f:
            content = f.read()
            
        if "[:2497]" in content:
            print("   ✅ FIXED: Description limit increased to 2500 chars")
        else:
            print("   ❌ ISSUE: Description limit still too low")
            
    except Exception as e:
        print(f"   ❌ Error checking file: {e}")

def test_status_active_fixes():
    """Test that status: ACTIVE is included in all requests"""
    
    print("\n🔍 TESTING STATUS: ACTIVE FIXES")
    print("=" * 50)
    
    # Test 1: Check ai_navigator_client.py
    print("✅ AI Navigator Client Status Check:")
    try:
        with open('ai_navigator_client.py', 'r') as f:
            content = f.read()
            
        if 'entity_data_with_status["status"] = "ACTIVE"' in content:
            print("   ✅ FIXED: Main client adds status: ACTIVE to all POST requests")
        else:
            print("   ❌ ISSUE: Main client missing status addition")
            
    except Exception as e:
        print(f"   ❌ Error checking file: {e}")
    
    # Test 2: Check backend/client.py
    print("\n✅ Backend Client Status Check:")
    try:
        with open('backend/client.py', 'r') as f:
            content = f.read()
            
        if 'entity_data_with_status["status"] = "ACTIVE"' in content:
            print("   ✅ FIXED: Backend client adds status: ACTIVE to all POST requests")
        else:
            print("   ❌ ISSUE: Backend client missing status addition")
            
    except Exception as e:
        print(f"   ❌ Error checking file: {e}")
    
    # Test 3: Check update methods include status
    print("\n✅ Update Methods Status Check:")
    files_to_check = [
        'working_processor.py',
        'multi_source_processor.py', 
        'cost_optimized_processor.py',
        'working_multi_source.py'
    ]
    
    for filename in files_to_check:
        try:
            with open(filename, 'r') as f:
                content = f.read()
                
            if 'update_data["status"] = "ACTIVE"' in content:
                print(f"   ✅ FIXED: {filename} adds status to PATCH requests")
            else:
                print(f"   ❌ ISSUE: {filename} missing status in updates")
                
        except Exception as e:
            print(f"   ❌ Error checking {filename}: {e}")

def test_entity_building_includes_status():
    """Test that entity building includes status by default"""
    
    print("\n🔍 TESTING ENTITY BUILDING STATUS")
    print("=" * 50)
    
    files_to_check = [
        'working_processor.py',
        'enhanced_item_processor.py'
    ]
    
    for filename in files_to_check:
        try:
            with open(filename, 'r') as f:
                content = f.read()
                
            if '"status": "ACTIVE"' in content:
                print(f"   ✅ VERIFIED: {filename} includes status in entity building")
            else:
                print(f"   ❌ ISSUE: {filename} missing status in entity building")
                
        except Exception as e:
            print(f"   ❌ Error checking {filename}: {e}")

def main():
    """Run all tests"""
    
    print("🧪 CRITICAL FIXES VERIFICATION")
    print("=" * 60)
    print("Testing fixes for:")
    print("1. Descriptions being cut off (now 1500-2500 chars)")
    print("2. Missing status: ACTIVE in POST/PATCH requests")
    print("=" * 60)
    
    test_description_length_fixes()
    test_status_active_fixes()
    test_entity_building_includes_status()
    
    print("\n🎯 SUMMARY")
    print("=" * 60)
    print("✅ Description Length Fixes:")
    print("   • AI prompts now ask for 1500-2500 character descriptions")
    print("   • All truncation limits increased from 400-600 to 2500 chars")
    print("   • Prompts emphasize 'EXTREMELY DETAILED' descriptions")
    
    print("\n✅ Status: ACTIVE Fixes:")
    print("   • All POST /entities requests include status: ACTIVE")
    print("   • All PATCH /entities requests include status: ACTIVE")
    print("   • Both main and backend clients fixed")
    print("   • All processor update methods fixed")
    
    print("\n🚀 NEXT STEPS:")
    print("   1. Restart the backend server to load the fixes")
    print("   2. Test with a real scraping job")
    print("   3. Verify descriptions are now much longer and detailed")
    print("   4. Verify all entities have status: ACTIVE in the database")

if __name__ == '__main__':
    main()
