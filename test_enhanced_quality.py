#!/usr/bin/env python3
"""
Test Enhanced Quality Improvements
Test the improved enhancement system with problematic tools like n8n and adult chat tools
"""

import requests
import json
import time
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8001"

def test_enhanced_quality():
    """Test the enhanced quality improvements with problematic tools"""
    
    print("🧪 TESTING ENHANCED QUALITY IMPROVEMENTS")
    print("=" * 60)
    
    # Test tools that previously had quality issues - using unique names to avoid duplicates
    test_tools = [
        {
            "name": "n8n Quality Test",
            "url": "https://n8n.io",
            "expected_improvements": [
                "Should mention 1000+ integrations, not generic 'many integrations'",
                "Should list specific integration categories",
                "Should mention workflow automation capabilities"
            ]
        },
        {
            "name": "Character AI Quality Test",
            "url": "https://character.ai",
            "expected_improvements": [
                "Should be categorized as AI Assistant/Conversational AI",
                "Should mention character creation and roleplay features",
                "Should not be categorized as Customer Service"
            ]
        },
        {
            "name": "Zapier Quality Test",
            "url": "https://zapier.com",
            "expected_improvements": [
                "Should mention 5000+ app integrations",
                "Should list automation workflow capabilities",
                "Should mention pricing per task/execution"
            ]
        },
        {
            "name": "Make Quality Test",
            "url": "https://make.com",
            "expected_improvements": [
                "Should mention visual automation builder",
                "Should list integration count and capabilities",
                "Should categorize as Integration Platform"
            ]
        }
    ]
    
    print(f"Testing {len(test_tools)} tools for quality improvements...")
    
    for i, tool in enumerate(test_tools):
        print(f"\n{'='*60}")
        print(f"🔍 TEST {i+1}/{len(test_tools)}: {tool['name']}")
        print(f"URL: {tool['url']}")
        print(f"Expected Improvements:")
        for improvement in tool['expected_improvements']:
            print(f"  - {improvement}")
        print(f"{'='*60}")
        
        # Start enhanced processing
        response = requests.post(f"{BASE_URL}/api/start-enhanced-scraping", json={
            "tools": [tool],
            "use_parallel": False,
            "use_phase3": True
        })
        
        if response.status_code != 200:
            logger.error(f"❌ Failed to start enhanced processing for {tool['name']}: {response.status_code}")
            logger.error(f"Response: {response.text}")
            continue
        
        result = response.json()
        job_id = result.get('job_id')
        logger.info(f"✅ Started enhanced processing job: {job_id}")
        
        # Monitor job progress
        max_attempts = 60  # 5 minutes max
        attempt = 0
        
        while attempt < max_attempts:
            time.sleep(5)
            attempt += 1
            
            # Check job status
            status_response = requests.get(f"{BASE_URL}/api/job-status/{job_id}")
            if status_response.status_code != 200:
                logger.error(f"❌ Failed to get job status: {status_response.status_code}")
                break
            
            job_data = status_response.json()
            status = job_data.get('status', 'unknown')
            progress = job_data.get('progress', 0)
            
            print(f"   📊 Progress: {progress:.1f}% - Status: {status}")
            
            if status == 'completed':
                print(f"   ✅ Job completed successfully!")

                # Check if tool was skipped due to duplicate
                result = job_data.get('result', {})
                if result.get('status') == 'skipped':
                    print(f"   ⚠️ Tool was skipped: {result.get('reason', 'Unknown reason')}")
                    break

                # Get detailed results - check multiple possible locations
                enhanced_data = None

                # Check if data is in result.data
                if job_data.get('result', {}).get('data'):
                    enhanced_data = job_data['result']['data']
                # Check if data is in results array
                elif job_data.get('results'):
                    enhanced_data = job_data['results'][0]
                # Check if data is directly in job_data
                elif 'enhanced_data' in job_data:
                    enhanced_data = job_data['enhanced_data']

                if enhanced_data:
                    analyze_enhancement_quality(tool['name'], enhanced_data, tool['expected_improvements'])
                else:
                    print(f"   ⚠️ No enhanced data found")
                    print(f"   📋 Available keys: {list(job_data.keys())}")
                    if 'result' in job_data:
                        print(f"   📋 Result keys: {list(job_data['result'].keys())}")
                break
            elif status == 'failed':
                print(f"   ❌ Job failed")
                errors = job_data.get('errors', [])
                for error in errors:
                    print(f"      Error: {error}")
                break
        else:
            print(f"   ⏰ Job timed out after {max_attempts * 5} seconds")
    
    print(f"\n🎉 Enhanced Quality Testing Complete!")

def analyze_enhancement_quality(tool_name: str, enhanced_data: dict, expected_improvements: list):
    """Analyze the quality of enhancement results"""

    print(f"\n📊 QUALITY ANALYSIS FOR {tool_name.upper()}")
    print("-" * 50)

    # Check if enhancement was successful
    if not enhanced_data:
        print("❌ No enhanced data found")
        return
    
    # Analyze key quality metrics
    quality_checks = []

    # Check description quality - handle both direct fields and nested tool_details
    description = enhanced_data.get('description', '')
    short_description = enhanced_data.get('short_description', '')

    # Also check tool_details if available
    tool_details = enhanced_data.get('tool_details', {})
    if tool_details and not description:
        description = tool_details.get('description', '')
    if tool_details and not short_description:
        short_description = tool_details.get('short_description', '')
    
    if len(description) > 500:
        quality_checks.append("✅ Description is comprehensive (>500 chars)")
    else:
        quality_checks.append(f"⚠️ Description is short ({len(description)} chars)")
    
    if len(short_description) > 150:
        quality_checks.append("✅ Short description is detailed (>150 chars)")
    else:
        quality_checks.append(f"⚠️ Short description is brief ({len(short_description)} chars)")
    
    # Check for generic phrases
    generic_phrases = ['AI-powered tool', 'enhance productivity', 'many integrations', 'various features']
    found_generic = []
    for phrase in generic_phrases:
        if phrase.lower() in description.lower() or phrase.lower() in short_description.lower():
            found_generic.append(phrase)
    
    if found_generic:
        quality_checks.append(f"⚠️ Found generic phrases: {', '.join(found_generic)}")
    else:
        quality_checks.append("✅ No generic phrases detected")
    
    # Check feature count
    features = enhanced_data.get('key_features', [])
    if not features and tool_details:
        features = tool_details.get('key_features', [])

    if len(features) >= 5:
        quality_checks.append(f"✅ Good feature count ({len(features)} features)")
    else:
        quality_checks.append(f"⚠️ Low feature count ({len(features)} features)")

    # Check integration mentions for automation tools
    if any(keyword in tool_name.lower() for keyword in ['n8n', 'zapier', 'make']):
        integration_keywords = ['1000+', '5000+', 'integrations', 'apps', 'services']
        found_integration_info = any(keyword in description.lower() for keyword in integration_keywords)
        if found_integration_info:
            quality_checks.append("✅ Mentions integration counts/capabilities")
        else:
            quality_checks.append("⚠️ Missing specific integration information")

        # Check for specific integration lists
        integrations = tool_details.get('integrations', [])
        if len(integrations) >= 5:
            quality_checks.append(f"✅ Good integration list ({len(integrations)} integrations listed)")
        else:
            quality_checks.append(f"⚠️ Few integrations listed ({len(integrations)} integrations)")

    # Check categories - look in multiple places
    categories = enhanced_data.get('categories', [])
    if not categories:
        # Categories might be IDs, try to get readable names
        category_ids = enhanced_data.get('category_ids', [])
        if category_ids:
            quality_checks.append(f"✅ Category IDs assigned: {len(category_ids)} categories")
        else:
            quality_checks.append("⚠️ No categories assigned")
    else:
        quality_checks.append(f"✅ Categories assigned: {', '.join(categories)}")
    
    # Print quality analysis
    for check in quality_checks:
        print(f"  {check}")
    
    # Print key data for manual review
    print(f"\n📝 KEY DATA FOR MANUAL REVIEW:")
    print(f"Short Description: {short_description}")
    print(f"Description Length: {len(description)} chars")
    if categories:
        print(f"Categories: {', '.join(categories)}")
    else:
        category_ids = enhanced_data.get('category_ids', [])
        print(f"Category IDs: {len(category_ids)} assigned")
    print(f"Features: {', '.join(features[:3])}{'...' if len(features) > 3 else ''}")

    # Show integration info for automation tools
    if any(keyword in tool_name.lower() for keyword in ['n8n', 'zapier', 'make']):
        integrations = tool_details.get('integrations', [])
        print(f"Integrations: {', '.join(integrations[:5])}{'...' if len(integrations) > 5 else ''}")
        print(f"Total Integrations Listed: {len(integrations)}")

    # Show use cases
    use_cases = tool_details.get('use_cases', [])
    if use_cases:
        print(f"Use Cases: {', '.join(use_cases[:2])}{'...' if len(use_cases) > 2 else ''}")
    
    # Check against expected improvements
    print(f"\n🎯 EXPECTED IMPROVEMENT CHECK:")
    for improvement in expected_improvements:
        print(f"  📋 {improvement}")
        # This would require more sophisticated analysis to automatically verify
        print(f"     → Manual verification required")

if __name__ == "__main__":
    test_enhanced_quality()
