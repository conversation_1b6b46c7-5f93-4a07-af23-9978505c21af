#!/usr/bin/env python3
"""
Test Enhanced Quality Improvements
Test the improved enhancement system with problematic tools like n8n and adult chat tools
"""

import requests
import json
import time
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8001"

def test_enhanced_quality():
    """Test the enhanced quality improvements with problematic tools"""
    
    print("🧪 TESTING ENHANCED QUALITY IMPROVEMENTS")
    print("=" * 60)
    
    # Test tools that previously had quality issues
    test_tools = [
        {
            "name": "n8n",
            "url": "https://n8n.io",
            "expected_improvements": [
                "Should mention 1000+ integrations, not generic 'many integrations'",
                "Should list specific integration categories",
                "Should mention workflow automation capabilities"
            ]
        },
        {
            "name": "Character.AI",
            "url": "https://character.ai",
            "expected_improvements": [
                "Should be categorized as AI Assistant/Conversational AI",
                "Should mention character creation and roleplay features",
                "Should not be categorized as Customer Service"
            ]
        },
        {
            "name": "Zapier",
            "url": "https://zapier.com",
            "expected_improvements": [
                "Should mention 5000+ app integrations",
                "Should list automation workflow capabilities",
                "Should mention pricing per task/execution"
            ]
        },
        {
            "name": "Make.com",
            "url": "https://make.com",
            "expected_improvements": [
                "Should mention visual automation builder",
                "Should list integration count and capabilities",
                "Should categorize as Integration Platform"
            ]
        }
    ]
    
    print(f"Testing {len(test_tools)} tools for quality improvements...")
    
    for i, tool in enumerate(test_tools):
        print(f"\n{'='*60}")
        print(f"🔍 TEST {i+1}/{len(test_tools)}: {tool['name']}")
        print(f"URL: {tool['url']}")
        print(f"Expected Improvements:")
        for improvement in tool['expected_improvements']:
            print(f"  - {improvement}")
        print(f"{'='*60}")
        
        # Start enhanced processing
        response = requests.post(f"{BASE_URL}/api/start-enhanced-scraping", json={
            "tools": [tool],
            "use_parallel": False,
            "use_phase3": True
        })
        
        if response.status_code != 200:
            logger.error(f"❌ Failed to start enhanced processing for {tool['name']}: {response.status_code}")
            logger.error(f"Response: {response.text}")
            continue
        
        result = response.json()
        job_id = result.get('job_id')
        logger.info(f"✅ Started enhanced processing job: {job_id}")
        
        # Monitor job progress
        max_attempts = 60  # 5 minutes max
        attempt = 0
        
        while attempt < max_attempts:
            time.sleep(5)
            attempt += 1
            
            # Check job status
            status_response = requests.get(f"{BASE_URL}/api/job-status/{job_id}")
            if status_response.status_code != 200:
                logger.error(f"❌ Failed to get job status: {status_response.status_code}")
                break
            
            job_data = status_response.json()
            status = job_data.get('status', 'unknown')
            progress = job_data.get('progress', 0)
            
            print(f"   📊 Progress: {progress:.1f}% - Status: {status}")
            
            if status == 'completed':
                print(f"   ✅ Job completed successfully!")
                
                # Get detailed results
                results = job_data.get('results', [])
                if results:
                    tool_result = results[0]  # First (and only) tool
                    analyze_enhancement_quality(tool['name'], tool_result, tool['expected_improvements'])
                else:
                    print(f"   ⚠️ No results found in job data")
                break
            elif status == 'failed':
                print(f"   ❌ Job failed")
                errors = job_data.get('errors', [])
                for error in errors:
                    print(f"      Error: {error}")
                break
        else:
            print(f"   ⏰ Job timed out after {max_attempts * 5} seconds")
    
    print(f"\n🎉 Enhanced Quality Testing Complete!")

def analyze_enhancement_quality(tool_name: str, result: dict, expected_improvements: list):
    """Analyze the quality of enhancement results"""
    
    print(f"\n📊 QUALITY ANALYSIS FOR {tool_name.upper()}")
    print("-" * 50)
    
    # Check if enhancement was successful
    if not result.get('enhanced_data'):
        print("❌ No enhanced data found")
        return
    
    enhanced_data = result['enhanced_data']
    
    # Analyze key quality metrics
    quality_checks = []
    
    # Check description quality
    description = enhanced_data.get('description', '')
    short_description = enhanced_data.get('short_description', '')
    
    if len(description) > 500:
        quality_checks.append("✅ Description is comprehensive (>500 chars)")
    else:
        quality_checks.append(f"⚠️ Description is short ({len(description)} chars)")
    
    if len(short_description) > 150:
        quality_checks.append("✅ Short description is detailed (>150 chars)")
    else:
        quality_checks.append(f"⚠️ Short description is brief ({len(short_description)} chars)")
    
    # Check for generic phrases
    generic_phrases = ['AI-powered tool', 'enhance productivity', 'many integrations', 'various features']
    found_generic = []
    for phrase in generic_phrases:
        if phrase.lower() in description.lower() or phrase.lower() in short_description.lower():
            found_generic.append(phrase)
    
    if found_generic:
        quality_checks.append(f"⚠️ Found generic phrases: {', '.join(found_generic)}")
    else:
        quality_checks.append("✅ No generic phrases detected")
    
    # Check feature count
    features = enhanced_data.get('key_features', [])
    if len(features) >= 5:
        quality_checks.append(f"✅ Good feature count ({len(features)} features)")
    else:
        quality_checks.append(f"⚠️ Low feature count ({len(features)} features)")
    
    # Check integration mentions for automation tools
    if tool_name.lower() in ['n8n', 'zapier', 'make.com']:
        integration_keywords = ['1000+', '5000+', 'integrations', 'apps', 'services']
        found_integration_info = any(keyword in description.lower() for keyword in integration_keywords)
        if found_integration_info:
            quality_checks.append("✅ Mentions integration counts/capabilities")
        else:
            quality_checks.append("⚠️ Missing specific integration information")
    
    # Check categories
    categories = enhanced_data.get('categories', [])
    if categories:
        quality_checks.append(f"✅ Categories assigned: {', '.join(categories)}")
    else:
        quality_checks.append("⚠️ No categories assigned")
    
    # Print quality analysis
    for check in quality_checks:
        print(f"  {check}")
    
    # Print key data for manual review
    print(f"\n📝 KEY DATA FOR MANUAL REVIEW:")
    print(f"Short Description: {short_description}")
    print(f"Categories: {', '.join(categories)}")
    print(f"Features: {', '.join(features[:3])}{'...' if len(features) > 3 else ''}")
    
    # Check against expected improvements
    print(f"\n🎯 EXPECTED IMPROVEMENT CHECK:")
    for improvement in expected_improvements:
        print(f"  📋 {improvement}")
        # This would require more sophisticated analysis to automatically verify
        print(f"     → Manual verification required")

if __name__ == "__main__":
    test_enhanced_quality()
