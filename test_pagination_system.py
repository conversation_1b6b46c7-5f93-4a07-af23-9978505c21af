#!/usr/bin/env python3
"""
Test script for the pagination system
Tests pagination state tracking, API endpoints, and spider integration
"""

import requests
import time
import json
import sys

API_BASE = "http://localhost:8001"

def test_api_endpoint(endpoint, method="GET", data=None, description=""):
    """Test an API endpoint and return the result"""
    print(f"\n🧪 Testing {method} {endpoint}")
    if description:
        print(f"   Description: {description}")
    
    try:
        if method == "GET":
            response = requests.get(f"{API_BASE}{endpoint}")
        elif method == "POST":
            response = requests.post(f"{API_BASE}{endpoint}", json=data)
        elif method == "PUT":
            response = requests.put(f"{API_BASE}{endpoint}", json=data)
        elif method == "DELETE":
            response = requests.delete(f"{API_BASE}{endpoint}")
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Success")
            return result
        else:
            print(f"   ❌ Failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return None

def test_pagination_state_api():
    """Test pagination state API endpoints"""
    print("\n" + "="*60)
    print("🔍 TESTING PAGINATION STATE API")
    print("="*60)
    
    # Test 1: Get all pagination states (should be empty initially)
    result = test_api_endpoint("/api/pagination-state", description="Get all pagination states")
    if result:
        print(f"   Total spiders with state: {result.get('total_spiders', 0)}")
    
    # Test 2: Get specific spider state (should be empty)
    result = test_api_endpoint("/api/pagination-state/futuretools", description="Get futuretools pagination state")
    if result:
        print(f"   Next start page: {result.get('next_start_page', 1)}")
    
    # Test 3: Manually set pagination state
    test_data = {
        "last_page_scraped": 5,
        "total_pages_discovered": 20,
        "notes": "Test pagination state"
    }
    result = test_api_endpoint("/api/pagination-state/futuretools", "PUT", test_data, 
                             "Manually set pagination state")
    
    # Test 4: Verify the state was set
    result = test_api_endpoint("/api/pagination-state/futuretools", description="Verify state was set")
    if result and result.get('state'):
        state = result['state']
        print(f"   Last page: {state.get('last_page_scraped')}")
        print(f"   Next start page: {result.get('next_start_page')}")
        print(f"   Notes: {state.get('notes')}")
    
    # Test 5: Reset pagination state
    result = test_api_endpoint("/api/pagination-state/futuretools", "DELETE", 
                             description="Reset pagination state")
    
    # Test 6: Verify state was reset
    result = test_api_endpoint("/api/pagination-state/futuretools", description="Verify state was reset")
    if result:
        print(f"   Next start page after reset: {result.get('next_start_page', 1)}")

def test_scraping_with_pagination():
    """Test scraping with pagination parameters"""
    print("\n" + "="*60)
    print("🕷️ TESTING SCRAPING WITH PAGINATION")
    print("="*60)
    
    # Test 1: Start scraping with pagination parameters
    scraping_data = {
        "spider_name": "futuretools",
        "max_items": 10,
        "start_page": 2,
        "max_pages": 2
    }
    
    print(f"\n🚀 Starting scraping job with pagination:")
    print(f"   Spider: {scraping_data['spider_name']}")
    print(f"   Max items: {scraping_data['max_items']}")
    print(f"   Start page: {scraping_data['start_page']}")
    print(f"   Max pages: {scraping_data['max_pages']}")
    
    result = test_api_endpoint("/api/start-scraping", "POST", scraping_data, 
                             "Start scraping with pagination")
    
    if result and result.get('success'):
        job_id = result.get('job_id')
        print(f"   Job ID: {job_id}")
        
        # Wait a bit for the job to start
        print("\n⏳ Waiting for job to process...")
        time.sleep(10)
        
        # Check pagination state after scraping
        print("\n📊 Checking pagination state after scraping:")
        state_result = test_api_endpoint("/api/pagination-state/futuretools", 
                                       description="Check state after scraping")
        
        if state_result and state_result.get('state'):
            state = state_result['state']
            print(f"   Items scraped: {state.get('total_items_scraped', 0)}")
            print(f"   Last page scraped: {state.get('last_page_scraped', 0)}")
            print(f"   Recommended next start: {state_result.get('next_start_page', 1)}")
        
        return True
    else:
        print("   ❌ Failed to start scraping job")
        return False

def test_resume_functionality():
    """Test resume functionality"""
    print("\n" + "="*60)
    print("🔄 TESTING RESUME FUNCTIONALITY")
    print("="*60)
    
    # Get current pagination state
    result = test_api_endpoint("/api/pagination-state/futuretools", 
                             description="Get current state for resume test")
    
    if result and result.get('state'):
        next_start_page = result.get('next_start_page', 1)
        print(f"   Will resume from page: {next_start_page}")
        
        # Start scraping without specifying start_page (should use pagination service)
        scraping_data = {
            "spider_name": "futuretools",
            "max_items": 5,
            "max_pages": 1
        }
        
        result = test_api_endpoint("/api/start-scraping", "POST", scraping_data, 
                                 "Resume scraping from last page")
        
        if result and result.get('success'):
            print(f"   ✅ Resume test successful")
            return True
    
    print("   ⚠️ No previous state found for resume test")
    return False

def main():
    """Run all pagination tests"""
    print("🧪 PAGINATION SYSTEM COMPREHENSIVE TEST")
    print("="*60)
    
    # Check if server is running
    try:
        response = requests.get(f"{API_BASE}/api/health")
        if response.status_code != 200:
            print("❌ Server is not running or not healthy")
            print("   Please start the server with: python complete_enhanced_server.py")
            sys.exit(1)
        print("✅ Server is running and healthy")
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        print("   Please start the server with: python complete_enhanced_server.py")
        sys.exit(1)
    
    # Run tests
    test_pagination_state_api()
    
    # Only run scraping tests if user confirms
    print("\n" + "="*60)
    response = input("🤔 Run actual scraping tests? This will take time and use API credits (y/N): ")
    if response.lower() in ['y', 'yes']:
        test_scraping_with_pagination()
        test_resume_functionality()
    else:
        print("⏭️ Skipping scraping tests")
    
    print("\n" + "="*60)
    print("🎉 PAGINATION SYSTEM TESTS COMPLETE")
    print("="*60)
    
    print("\n📋 SUMMARY:")
    print("✅ Pagination state service implemented")
    print("✅ API endpoints for pagination management")
    print("✅ Frontend pagination controls added")
    print("✅ Spider pagination parameter support")
    print("✅ Automatic state tracking after scraping")
    
    print("\n🚀 NEXT STEPS:")
    print("1. Test with real scraping jobs")
    print("2. Monitor pagination state persistence")
    print("3. Verify resume functionality works correctly")
    print("4. Check that existing functionality is preserved")

if __name__ == "__main__":
    main()
