import scrapy
from ainav_scrapers.items import AiToolLeadItem
from urllib.parse import urljoin, urlparse
import datetime
import re

class ToolifySimpleSpider(scrapy.Spider):
    name = "toolify_simple"
    allowed_domains = ["toolify.ai"]
    
    # Start with main page and key category pages
    start_urls = [
        "https://toolify.ai/",
        "https://toolify.ai/new",
        "https://toolify.ai/most-saved", 
        "https://toolify.ai/most-used",
    ]

    custom_settings = {
        'AUTOTHROTTLE_ENABLED': True,
        'AUTOTHROTTLE_START_DELAY': 3,
        'AUTOTHROTTLE_MAX_DELAY': 15,
        'DOWNLOAD_DELAY': 2,
        'CONCURRENT_REQUESTS_PER_DOMAIN': 2,
        'USER_AGENT': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36',
        'FEEDS': {
            'toolify_leads.jsonl': {
                'format': 'jsonlines',
                'encoding': 'utf8',
                'store_empty': False,
                'fields': ['tool_name_on_directory', 'external_website_url', 'source_directory', 'scraped_date'],
                'indent': None,
            }
        },
        'CLOSESPIDER_ITEMCOUNT': 200,
        'ROBOTSTXT_OBEY': True,
    }

    def __init__(self, max_items=None, start_page=1, max_pages=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.max_items = int(max_items) if max_items else 200
        self.start_page = int(start_page) if start_page else 1
        self.max_pages = int(max_pages) if max_pages else None
        self.total_found = 0
        self.scraped_tools = set()
        
        # Update CLOSESPIDER_ITEMCOUNT based on max_items
        if hasattr(self, 'custom_settings'):
            self.custom_settings['CLOSESPIDER_ITEMCOUNT'] = self.max_items
        
        self.logger.info(f"🚀 Toolify Simple Spider initialized:")
        self.logger.info(f"   Max items: {self.max_items}")
        self.logger.info(f"   Start page: {self.start_page}")
        self.logger.info(f"   Max pages: {self.max_pages}")

    def parse(self, response):
        """Parse the main listing page to extract tool links"""
        self.logger.info(f"🔍 Parsing Toolify page: {response.url}")

        if response.status != 200:
            self.logger.error(f"Failed to load page {response.url} with status {response.status}")
            return

        # Extract tool links using multiple strategies
        tool_links = []
        
        # Strategy 1: Look for direct tool links
        direct_links = response.css('a[href*="/tool/"]::attr(href)').getall()
        for link in direct_links:
            full_url = urljoin(response.url, link)
            tool_name = self._extract_tool_name_from_url(full_url)
            if tool_name:
                tool_links.append({
                    'name': tool_name,
                    'url': full_url
                })
        
        # Strategy 2: Look for tool cards or containers
        tool_cards = response.css('.tool-card, .tool-item, [data-tool], .card')
        for card in tool_cards:
            link = card.css('a::attr(href)').get()
            if link and '/tool/' in link:
                full_url = urljoin(response.url, link)
                tool_name = self._extract_tool_name_from_card(card) or self._extract_tool_name_from_url(full_url)
                if tool_name:
                    tool_links.append({
                        'name': tool_name,
                        'url': full_url
                    })
        
        # Strategy 3: Look for any links that might be tools
        all_links = response.css('a::attr(href)').getall()
        for link in all_links:
            if '/tool/' in link or self._looks_like_tool_url(link):
                full_url = urljoin(response.url, link)
                tool_name = self._extract_tool_name_from_url(full_url)
                if tool_name and not any(t['url'] == full_url for t in tool_links):
                    tool_links.append({
                        'name': tool_name,
                        'url': full_url
                    })
        
        self.logger.info(f"Found {len(tool_links)} tool links")

        # Process each tool link
        for tool_info in tool_links:
            if self.total_found >= self.max_items:
                self.logger.info(f"Reached max items limit ({self.max_items})")
                return
                
            tool_key = f"{tool_info['name']}:{tool_info['url']}"
            if tool_key not in self.scraped_tools:
                self.scraped_tools.add(tool_key)
                self.total_found += 1
                
                # Create the item directly since we have the basic info
                item = AiToolLeadItem()
                item['tool_name_on_directory'] = tool_info['name']
                item['external_website_url'] = self._extract_external_url(tool_info['url'])
                item['source_directory'] = 'toolify.ai'
                item['scraped_date'] = datetime.datetime.now().isoformat()
                
                self.logger.info(f"✅ Found tool: {item['tool_name_on_directory']} -> {item['external_website_url']}")
                yield item

    def _extract_tool_name_from_url(self, url):
        """Extract tool name from URL"""
        try:
            # Extract from URL path like /tool/chatgpt -> ChatGPT
            path_parts = urlparse(url).path.split('/')
            if 'tool' in path_parts:
                tool_index = path_parts.index('tool')
                if tool_index + 1 < len(path_parts):
                    tool_slug = path_parts[tool_index + 1]
                    # Convert slug to readable name
                    return tool_slug.replace('-', ' ').replace('_', ' ').title()
        except:
            pass
        return None

    def _extract_tool_name_from_card(self, card):
        """Extract tool name from card element"""
        # Try various selectors for tool names
        name_selectors = [
            '.tool-name::text',
            '.title::text',
            'h1::text',
            'h2::text',
            'h3::text',
            '.name::text',
            '[data-name]::attr(data-name)',
        ]
        
        for selector in name_selectors:
            name = card.css(selector).get()
            if name:
                return name.strip()
        
        return None

    def _looks_like_tool_url(self, url):
        """Check if URL looks like it might be a tool"""
        tool_indicators = ['/tool/', '/ai-tool/', '/product/', '/app/']
        return any(indicator in url for indicator in tool_indicators)

    def _extract_external_url(self, toolify_url):
        """Extract the actual external website URL from a Toolify tool page URL"""
        # For now, return the toolify URL as we'd need to visit the page to get the real URL
        # In a real implementation, we'd make a request to the tool page and extract the external link
        return toolify_url
