"""
Working Cost-Optimized Processor
XAI Grok-3 (free) + Minimal Perplexity + Robust fallbacks
"""

import sys
sys.path.append('/app')

import json
import logging
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse, urljoin
import re
from ai_navigator_client import AINavigatorClient
from enhanced_taxonomy_service import EnhancedTaxonomyService
from entity_type_detector import EntityTypeDetector
from centralized_url_resolver import CentralizedURLResolver
from contact_info_extractor import ContactInfoExtractor
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WorkingProcessor:
    def __init__(self, xai_api_key: str, perplexity_api_key: str):
        self.client = AINavigatorClient()
        self.taxonomy_service = EnhancedTaxonomyService(self.client)
        self.entity_detector = EntityTypeDetector()
        self.url_resolver = CentralizedURLResolver()
        self.contact_extractor = ContactInfoExtractor()
        self.xai_api_key = xai_api_key
        self.perplexity_api_key = perplexity_api_key
        
    def process_tool(self, tool_data: dict) -> bool:
        """Process a single tool with robust fallbacks"""
        tool_name = tool_data.get('tool_name_on_directory', '')
        futuretools_url = tool_data.get('external_website_url', '')
        
        logger.info(f"🚀 Processing: {tool_name}")
        
        try:
            # Step 1: Resolve actual website URL
            actual_url = self._resolve_actual_url(futuretools_url)
            logger.info(f"  🔍 Resolved URL: {actual_url}")
            
            # Step 2: Check if entity exists
            existing_entity = self._find_existing_entity(tool_name, actual_url)
            
            # Step 3: Scrape website comprehensively
            website_data = self._scrape_website(actual_url)
            
            # Step 4: AI Enhancement with robust fallbacks
            enhanced_data = self._enhance_with_ai_pipeline(tool_name, website_data, actual_url)
            
            # Step 5: Build entity with validation
            entity_data = self._build_validated_entity(tool_name, actual_url, website_data, enhanced_data)
            
            # Step 6: Create or update
            if existing_entity:
                result = self._update_entity(existing_entity['id'], entity_data)
                action = "Updated"
            else:
                result = self._create_entity(entity_data)
                action = "Created"
            
            if result:
                logger.info(f"  ✅ {action} entity: {tool_name}")
                return True
            else:
                logger.error(f"  ❌ Failed to {action.lower()} entity: {tool_name}")
                return False
                
        except Exception as e:
            logger.error(f"  ❌ Error processing {tool_name}: {str(e)}")
            return False
    
    def _resolve_actual_url(self, futuretools_url: str) -> str:
        """Resolve FutureTools redirect to actual website using centralized resolver"""
        try:
            # Use centralized URL resolver for robust redirect handling
            if 'futuretools.link' in futuretools_url or 'futuretools.io' in futuretools_url:
                return self.url_resolver.resolve_futuretools_redirect(futuretools_url)
            else:
                return self.url_resolver.resolve_final_url(futuretools_url)
        except Exception as e:
            logger.warning(f"Error resolving URL: {str(e)}")
            return futuretools_url
    
    def _find_existing_entity(self, name: str, website_url: str) -> dict:
        """Check if entity already exists"""
        try:
            response = requests.get(
                f"{self.client.base_url}/entities",
                headers=self.client._get_headers(),
                params={"search": name, "limit": 10},
                timeout=10
            )
            
            if response.status_code == 200:
                entities = response.json()
                if isinstance(entities, dict) and 'data' in entities:
                    entities = entities['data']
                
                for entity in entities:
                    if (entity.get('name', '').lower() == name.lower() or 
                        entity.get('website_url', '') == website_url):
                        logger.info(f"  🔄 Found existing entity: {entity.get('id')}")
                        return entity
            
            return None
            
        except Exception as e:
            logger.error(f"Error checking existing entity: {str(e)}")
            return None
    
    def _scrape_website(self, url: str) -> dict:
        """Comprehensive website scraping"""
        try:
            response = requests.get(url, timeout=15, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            if response.status_code != 200:
                return {}
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract comprehensive data including contact information
            data = {
                'title': self._get_title(soup),
                'meta_description': self._get_meta_description(soup),
                'logo_url': self._extract_logo(soup, url),
                'social_links': self._extract_social_links(response.text),
                'headings': self._extract_headings(soup),
                'paragraphs': self._extract_paragraphs(soup),
                'content_text': soup.get_text()[:2000],  # First 2000 chars
                'domain': urlparse(url).netloc
            }

            # Extract contact information using the contact extractor
            try:
                contact_info = self.contact_extractor.extract_contact_info(url, response.text)
                data.update(contact_info)
            except Exception as e:
                logger.warning(f"Error extracting contact info: {str(e)}")
                data.update({
                    'support_email': None,
                    'has_live_chat': False,
                    'community_url': None,
                    'social_links': data.get('social_links', {})
                })
            
            logger.info(f"  📊 Scraped website successfully")
            return data
            
        except Exception as e:
            logger.warning(f"Error scraping website: {str(e)}")
            return {}
    
    def _enhance_with_ai_pipeline(self, tool_name: str, website_data: dict, url: str) -> dict:
        """AI enhancement with multiple fallbacks"""
        
        # Try XAI first (free credits)
        xai_data = self._try_xai_enhancement(tool_name, website_data, url)
        
        # If XAI fails, try Perplexity (minimal cost)
        if not xai_data or not xai_data.get('short_description'):
            logger.info(f"  🔄 XAI failed, trying Perplexity...")
            perplexity_data = self._try_perplexity_enhancement(tool_name, website_data, url)
            xai_data.update(perplexity_data)
        
        # If both fail, create basic data from scraped content
        if not xai_data or not xai_data.get('short_description'):
            logger.info(f"  🔄 AI failed, creating from scraped data...")
            xai_data = self._create_from_scraped_data(tool_name, website_data, url)
        
        return xai_data
    
    def _try_xai_enhancement(self, tool_name: str, website_data: dict, url: str) -> dict:
        """Use XAI with correct OpenAI-compatible API"""
        try:
            content_summary = f"""
Tool: {tool_name}
Website: {url}
Title: {website_data.get('title', '')}
Description: {website_data.get('meta_description', '')}
Content: {website_data.get('content_text', '')[:1000]}
"""
            
            prompt = f"""
Analyze this tool and extract information. Return ONLY valid JSON:

{content_summary}

{{
  "short_description": "Specific description in 1 sentence (max 150 chars)",
  "description": "Detailed description in 2-3 sentences (max 400 chars)",
  "key_features": ["feature1", "feature2", "feature3"],
  "use_cases": ["use_case1", "use_case2"],
  "categories": ["category1", "category2"],
  "tags": ["tag1", "tag2", "tag3"],
  "pricing_model": "FREEMIUM",
  "target_audience": ["audience1", "audience2"]
}}
"""

            response = requests.post(
                "https://api.x.ai/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {self.xai_api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "grok-3-beta",  # Use correct model name
                    "messages": [
                        {"role": "system", "content": "You are an expert at analyzing websites. Always respond with valid JSON only. Do not include any explanation or markdown formatting."},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.1,
                    "max_tokens": 1000
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content'].strip()
                
                # Clean content - remove markdown formatting if present
                if content.startswith('```json'):
                    content = content.replace('```json', '').replace('```', '')
                elif content.startswith('```'):
                    content = content.replace('```', '')
                
                content = content.strip()
                
                # Extract JSON
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = content[json_start:json_end]
                    enhanced_data = json.loads(json_str)
                    logger.info(f"  🤖 XAI enhanced {tool_name} successfully")
                    return enhanced_data
                else:
                    logger.warning(f"  ⚠️ XAI response missing JSON brackets: {content[:100]}")
            else:
                logger.warning(f"  ⚠️ XAI failed with status: {response.status_code}, body: {response.text[:200]}")
            
            return {}
            
        except json.JSONDecodeError as e:
            logger.warning(f"  ⚠️ XAI JSON decode error: {str(e)}")
            return {}
        except Exception as e:
            logger.warning(f"  ⚠️ XAI error: {str(e)}")
            return {}
    
    def _try_perplexity_enhancement(self, tool_name: str, website_data: dict, url: str) -> dict:
        """Try Perplexity enhancement (minimal)"""
        try:
            prompt = f"""
Analyze "{tool_name}" (website: {url}) and return JSON:

{{
  "short_description": "Brief description (max 150 chars)",
  "description": "Detailed description (max 400 chars)",
  "key_features": ["feature1", "feature2"],
  "use_cases": ["use_case1", "use_case2"],
  "founded_year": 2023,
  "employee_count_range": "C1_10"
}}
"""

            response = requests.post(
                "https://api.perplexity.ai/chat/completions",
                headers={
                    "Authorization": f"Bearer {self.perplexity_api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "llama-3.1-sonar-small-128k-online",
                    "messages": [
                        {"role": "system", "content": "Provide JSON format responses only."},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.1,
                    "max_tokens": 500
                },
                timeout=20
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    enhanced_data = json.loads(content[json_start:json_end])
                    logger.info(f"  💰 Perplexity enhanced {tool_name}")
                    return enhanced_data
            
            logger.warning(f"  ⚠️ Perplexity failed with status: {response.status_code}")
            return {}
            
        except Exception as e:
            logger.warning(f"  ⚠️ Perplexity error: {str(e)}")
            return {}
    
    def _create_from_scraped_data(self, tool_name: str, website_data: dict, url: str) -> dict:
        """Create basic data from scraped content"""
        
        title = website_data.get('title', '')
        meta_desc = website_data.get('meta_description', '')
        content = website_data.get('content_text', '')
        
        # Create basic descriptions
        if meta_desc and len(meta_desc) > 20:
            short_desc = meta_desc[:150]
            long_desc = meta_desc[:400]
        elif title and len(title) > 10:
            short_desc = f"{tool_name} - {title}"[:150]
            long_desc = f"{tool_name} is a professional tool that provides {title.lower()} capabilities for businesses and individuals."[:400]
        else:
            short_desc = f"{tool_name} is an AI-powered platform for professional use."
            long_desc = f"{tool_name} provides advanced AI capabilities and tools designed to enhance productivity and streamline workflows for modern businesses."
        
        # Basic categorization based on content
        categories = ["AI & Machine Learning"]
        tags = ["AI-Powered", "Professional"]
        features = ["Professional Tools", "User-Friendly Interface"]
        use_cases = ["Business Applications", "Productivity Enhancement"]
        
        # Smart categorization based on content
        content_lower = content.lower()
        if any(word in content_lower for word in ['chat', 'conversation', 'assistant']):
            categories.append("Communication")
            tags.append("Conversational AI")
            features.append("Chat Interface")
        elif any(word in content_lower for word in ['image', 'photo', 'visual']):
            categories.append("Content Creation")
            tags.append("Visual AI")
            features.append("Image Processing")
        elif any(word in content_lower for word in ['data', 'analytics', 'insight']):
            categories.append("Analytics")
            tags.append("Data Analysis")
            features.append("Analytics Dashboard")
        
        return {
            'short_description': short_desc,
            'description': long_desc,
            'key_features': features[:5],
            'use_cases': use_cases[:3],
            'categories': categories[:2],
            'tags': tags[:4],
            'pricing_model': 'FREEMIUM',
            'target_audience': ['Business Professionals', 'Teams'],
            'has_free_tier': True,
            'api_access': False,
            'mobile_support': False
        }
    
    def _build_validated_entity(self, name: str, url: str, website_data: dict, enhanced_data: dict) -> dict:
        """Build entity with proper validation"""
        
        # Get entity type
        entity_type = self.entity_detector.detect_entity_type(name, url)
        entity_type_id = self._get_entity_type_id(entity_type)
        
        # Map taxonomy with UUID validation
        category_ids = self.taxonomy_service.map_categories(enhanced_data.get('categories', []))
        tag_ids = self.taxonomy_service.map_tags(enhanced_data.get('tags', []))
        feature_ids = self.taxonomy_service.map_features(enhanced_data.get('key_features', []))
        
        # Validate UUIDs and filter invalid ones
        def is_valid_uuid(uuid_string):
            try:
                import uuid
                uuid.UUID(uuid_string)
                return True
            except (ValueError, TypeError):
                return False
        
        category_ids = [id for id in category_ids if is_valid_uuid(id)]
        tag_ids = [id for id in tag_ids if is_valid_uuid(id)]
        feature_ids = [id for id in feature_ids if is_valid_uuid(id)]
        
        # Ensure required fields
        if not category_ids and self.taxonomy_service.categories_map:
            first_cat = list(self.taxonomy_service.categories_map.values())[0]
            category_ids = [first_cat['id']]
        
        if not tag_ids:
            # Add default tags
            default_tags = self.taxonomy_service.map_tags(['AI-Powered'])
            if default_tags:
                tag_ids = default_tags
        
        # Validate and clean strings - ALLOW MUCH LONGER SHORT DESCRIPTIONS
        short_desc = str(enhanced_data.get('short_description', f"{name} - AI-powered solution"))[:400]
        long_desc = str(enhanced_data.get('description', f"{name} provides professional AI capabilities."))[:2500]
        
        # Build entity
        entity = {
            'name': name,
            'website_url': url,
            'entity_type_id': entity_type_id,
            'short_description': short_desc,
            'description': long_desc,
            'category_ids': category_ids,
            'tag_ids': tag_ids if tag_ids else [],
            'feature_ids': feature_ids,
            'meta_title': f"{name} | AI Navigator",
            'meta_description': short_desc,
            'ref_link': url,
            'affiliate_status': 'NONE',
            'status': 'ACTIVE'
        }
        
        # Add optional fields
        if website_data.get('logo_url'):
            entity['logo_url'] = website_data['logo_url']
        
        if website_data.get('social_links'):
            entity['social_links'] = website_data['social_links']
        
        if enhanced_data.get('founded_year'):
            entity['founded_year'] = enhanced_data['founded_year']
        
        if enhanced_data.get('employee_count_range'):
            entity['employee_count_range'] = enhanced_data['employee_count_range']
        
        # Build comprehensive tool details following user schema
        entity['tool_details'] = {
            # Core functionality
            'learning_curve': enhanced_data.get('learning_curve', 'MEDIUM'),
            'key_features': enhanced_data.get('key_features', ['Professional Tools'])[:10],
            'has_free_tier': enhanced_data.get('has_free_tier', True),
            'use_cases': enhanced_data.get('use_cases', ['Business Applications'])[:8],

            # Pricing
            'pricing_model': enhanced_data.get('pricing_model', 'FREEMIUM'),
            'price_range': enhanced_data.get('price_range', 'MEDIUM'),
            'pricing_details': enhanced_data.get('pricing_details', ''),
            'pricing_url': enhanced_data.get('pricing_url'),

            # Technical
            'technical_level': enhanced_data.get('technical_level', 'BEGINNER'),
            'programming_languages': enhanced_data.get('programming_languages', []),
            'platforms': enhanced_data.get('platforms', enhanced_data.get('supported_os', ['Web Browser']))[:5],
            'api_access': enhanced_data.get('api_access', False),
            'api_documentation_url': enhanced_data.get('api_documentation_url'),

            # Support (HIGH PRIORITY)
            'support_email': website_data.get('support_email') or enhanced_data.get('support_email'),
            'has_live_chat': website_data.get('has_live_chat', False) or enhanced_data.get('has_live_chat', False),
            'community_url': website_data.get('community_url') or enhanced_data.get('community_url'),

            # Features & Capabilities
            'integrations': enhanced_data.get('integrations', ['Standard APIs'])[:8],
            'target_audience': enhanced_data.get('target_audience', ['Business Professionals'])[:5],
            'mobile_support': enhanced_data.get('mobile_support', False),
            'customization_level': enhanced_data.get('customization_level', 'MEDIUM'),
            'trial_available': enhanced_data.get('trial_available', True),
            'demo_available': enhanced_data.get('demo_available', False),
            'open_source': enhanced_data.get('open_source', False)
        }
        
        return entity
    
    def _get_entity_type_id(self, entity_type_slug: str) -> str:
        """Get entity type ID from API"""
        try:
            response = requests.get(
                f"{self.client.base_url}/entity-types",
                headers=self.client._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                entity_types = response.json()
                for et in entity_types:
                    if et.get('slug') == entity_type_slug:
                        return et.get('id')
            
            return "fd181400-c9e6-431c-a8bd-c068d0491aba"  # ai-tool fallback
            
        except Exception as e:
            logger.error(f"Error getting entity type ID: {str(e)}")
            return "fd181400-c9e6-431c-a8bd-c068d0491aba"
    
    def _create_entity(self, entity_data: dict) -> bool:
        """Create new entity"""
        try:
            result = self.client.create_entity(entity_data)
            return bool(result)
        except Exception as e:
            logger.error(f"Error creating entity: {str(e)}")
            return False
    
    def _update_entity(self, entity_id: str, entity_data: dict) -> bool:
        """Update existing entity"""
        try:
            update_data = entity_data.copy()
            update_data.pop('name', None)  # Don't change name

            # CRITICAL: Ensure status is ACTIVE for all updates
            update_data["status"] = "ACTIVE"

            response = requests.patch(
                f"{self.client.base_url}/entities/{entity_id}",
                headers=self.client._get_headers(),
                json=update_data,
                timeout=30
            )
            
            return response.status_code in [200, 201]
        except Exception as e:
            logger.error(f"Error updating entity: {str(e)}")
            return False
    
    # Helper methods for scraping
    def _get_title(self, soup):
        title = soup.find('title')
        return title.get_text().strip() if title else ""
    
    def _get_meta_description(self, soup):
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        return meta_desc.get('content', '').strip() if meta_desc else ""
    
    def _extract_logo(self, soup, base_url):
        selectors = [
            'img[alt*="logo" i]', '.logo img', 'header img', '.navbar img',
            'img[src*="logo" i]', 'link[rel="apple-touch-icon"]', 'link[rel="icon"]'
        ]
        
        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                src = element.get('src') or element.get('href')
                if src:
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = urljoin(base_url, src)
                    if src.startswith('http'):
                        return src
        
        # Fallback
        domain = urlparse(base_url).netloc
        return f"https://www.google.com/s2/favicons?sz=128&domain={domain}"
    
    def _extract_social_links(self, page_text):
        patterns = {
            'twitter': r'twitter\.com/([a-zA-Z0-9_]{1,15})',
            'linkedin': r'linkedin\.com/company/([a-zA-Z0-9\-]{1,50})',
            'github': r'github\.com/([a-zA-Z0-9\-_]{1,39})'
        }
        
        social_links = {}
        for platform, pattern in patterns.items():
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                handle = matches[0]
                if handle and not any(skip in handle.lower() for skip in ['home', 'login', 'about']):
                    social_links[platform] = handle
        
        return social_links
    
    def _extract_headings(self, soup):
        headings = []
        for h in soup.find_all(['h1', 'h2', 'h3']):
            text = h.get_text().strip()
            if text and 5 < len(text) < 100:
                headings.append(text)
        return headings[:10]
    
    def _extract_paragraphs(self, soup):
        paragraphs = []
        for p in soup.find_all('p'):
            text = p.get_text().strip()
            if text and 50 < len(text) < 500:
                paragraphs.append(text)
        return paragraphs[:5]

def main():
    """Test the working processor"""
    
    XAI_API_KEY = "************************************************************************************"
    PERPLEXITY_API_KEY = "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"
    
    processor = WorkingProcessor(XAI_API_KEY, PERPLEXITY_API_KEY)
    
    # Load tools
    tools_file = '/app/ai-navigator-scrapers/futuretools_leads.jsonl'
    with open(tools_file, 'r') as f:
        tools = [json.loads(line.strip()) for line in f]
    
    # Test with last 3 tools from the dataset
    available_tools = len(tools)
    logger.info(f"📊 Total tools available: {available_tools}")
    logger.info(f"🚀 Testing working processor with last 3 tools...")
    
    success_count = 0
    test_tools = tools[-3:]  # Last 3 tools
    
    for i, tool in enumerate(test_tools):
        logger.info(f"\n{'='*60}")
        logger.info(f"Processing tool {i+1}/3: {tool.get('tool_name_on_directory', 'Unknown')}")
        
        success = processor.process_tool(tool)
        if success:
            success_count += 1
        
        time.sleep(2)  # Be respectful
    
    logger.info(f"\n🎉 SUCCESS RATE: {success_count}/3 tools ({(success_count/3)*100:.1f}%)")

if __name__ == "__main__":
    main()