#!/usr/bin/env python3
"""
Test script to verify the duplicate detection fix is working
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_item_processor import EnhancedItemProcessor
from ai_navigator_client import AINavigatorClient
from enhanced_taxonomy_service import EnhancedTaxonomyService
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def test_duplicate_detection_fix():
    """Test that the unique name generation bug is fixed"""
    
    print("🧪 TESTING DUPLICATE DETECTION FIX")
    print("=" * 50)
    
    try:
        # Initialize the processor
        client = AINavigatorClient()
        taxonomy_service = EnhancedTaxonomyService(client)
        processor = EnhancedItemProcessor(client, taxonomy_service)
        
        # Test with a tool that should exist (Notion was just created)
        test_entity = {
            "name": "Notion AI",
            "url": "https://www.notion.com/"
        }
        
        print(f"🔍 Testing: {test_entity['name']}")
        print(f"   URL: {test_entity['url']}")
        print()
        
        # This is the method that should detect duplicates and return None
        lead_data = {
            "tool_name_on_directory": test_entity["name"],
            "external_website_url": test_entity["url"]
        }
        
        print("🔄 Calling process_lead_item()...")
        result = processor.process_lead_item(lead_data)
        
        if result is None:
            print("✅ SUCCESS! Duplicate detected - processing skipped")
            print("💰 API credits saved!")
            print("🎉 The unique name generation bug is FIXED!")
            return True
        else:
            print("❌ FAILURE! Entity was processed instead of being skipped")
            print("💸 API credits wasted!")
            print("🚨 The bug is NOT fixed!")
            return False
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_new_entity():
    """Test that new entities are still processed"""
    
    print("\n🧪 TESTING NEW ENTITY PROCESSING")
    print("=" * 50)
    
    try:
        # Initialize the processor
        client = AINavigatorClient()
        taxonomy_service = EnhancedTaxonomyService(client)
        processor = EnhancedItemProcessor(client, taxonomy_service)
        
        # Test with a completely new tool
        test_entity = {
            "name": "Test Tool XYZ 12345",
            "url": "https://example-test-tool-xyz-12345.com"
        }
        
        print(f"🔍 Testing: {test_entity['name']}")
        print(f"   URL: {test_entity['url']}")
        print()
        
        # This should NOT be detected as duplicate
        lead_data = {
            "tool_name_on_directory": test_entity["name"],
            "external_website_url": test_entity["url"]
        }
        
        print("🔄 Calling process_lead_item()...")
        result = processor.process_lead_item(lead_data)
        
        if result is not None:
            print("✅ SUCCESS! New entity processed correctly")
            print("🎯 No false positive duplicate detection")
            return True
        else:
            print("❌ FAILURE! New entity was incorrectly skipped")
            print("🚨 False positive duplicate detection!")
            return False
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 DUPLICATE DETECTION FIX VERIFICATION")
    print("=" * 60)
    print()
    
    # Test 1: Duplicate detection
    duplicate_test_passed = test_duplicate_detection_fix()
    
    # Test 2: New entity processing
    new_entity_test_passed = test_new_entity()
    
    print("\n🏁 FINAL RESULTS:")
    print("=" * 30)
    print(f"✅ Duplicate Detection: {'PASS' if duplicate_test_passed else 'FAIL'}")
    print(f"✅ New Entity Processing: {'PASS' if new_entity_test_passed else 'FAIL'}")
    print()
    
    if duplicate_test_passed and new_entity_test_passed:
        print("🎉 ALL TESTS PASSED! The fix is working correctly!")
        print("✅ No more 'Tool 2', 'Tool 3' generation")
        print("💰 API credits are being saved")
        print("🚀 Ready for production!")
    else:
        print("🚨 TESTS FAILED! The fix needs more work!")
    
    sys.exit(0 if (duplicate_test_passed and new_entity_test_passed) else 1)
