// Test script to verify frontend-backend communication
// Run this in the browser console on localhost:3000

console.log('🧪 Testing Frontend-Backend Communication...');

// Test 1: Check if backend is reachable
async function testBackendHealth() {
  try {
    const response = await fetch('/api/health');
    const data = await response.json();
    console.log('✅ Backend Health Check:', data);
    return true;
  } catch (error) {
    console.error('❌ Backend Health Check Failed:', error);
    return false;
  }
}

// Test 2: Test single URL processing
async function testSingleUrlProcessing() {
  try {
    console.log('🚀 Starting single URL processing test...');
    
    // Create job
    const createResponse = await fetch('/api/process-single-url', {
      method: 'POST',
      headers: {'Content-Type': 'application/json'},
      body: JSON.stringify({
        name: 'Test Tool',
        url: 'https://example.com',
        use_parallel: true,
        use_phase3: true
      })
    });
    
    const createData = await createResponse.json();
    console.log('📝 Job Created:', createData);
    
    if (!createData.success) {
      console.error('❌ Failed to create job');
      return;
    }
    
    const jobId = createData.job_id;
    
    // Poll job status
    for (let i = 0; i < 10; i++) {
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
      
      const statusResponse = await fetch(`/api/job-status/${jobId}`);
      const statusData = await statusResponse.json();
      
      console.log(`📊 Job Status (${i+1}/10):`, {
        status: statusData.status,
        progress: statusData.progress,
        results_status: statusData.results?.status
      });
      
      if (statusData.status === 'completed' || statusData.status === 'failed') {
        console.log('🏁 Job finished:', statusData);
        break;
      }
    }
    
  } catch (error) {
    console.error('❌ Single URL Processing Test Failed:', error);
  }
}

// Run tests
async function runAllTests() {
  console.log('🧪 Starting Frontend-Backend Tests...');
  
  const healthOk = await testBackendHealth();
  if (!healthOk) {
    console.error('❌ Backend not reachable, stopping tests');
    return;
  }
  
  await testSingleUrlProcessing();
  
  console.log('✅ All tests completed');
}

// Export for manual execution
window.testFrontendBackend = runAllTests;

console.log('🎯 Run window.testFrontendBackend() to start tests');
